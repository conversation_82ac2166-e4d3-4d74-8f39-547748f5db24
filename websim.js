/**
 * Websim.js - A simple client for the Ollama API
 * This file provides a compatible interface for the existing application
 * to work with Ollama's Llama 3.1 model
 */

// Store student stress data globally
let studentStressData = {
    lastPrediction: null,
    studentData: null
};

// Function to update student stress data
function updateStressData(prediction, data) {
    studentStressData.lastPrediction = prediction;
    studentStressData.studentData = data;
    console.log('Student stress data updated:', studentStressData);
}

// Listen for custom events from the main application
document.addEventListener('stressDataUpdated', (event) => {
    if (event.detail && event.detail.prediction) {
        updateStressData(event.detail.prediction, event.detail.studentData);
    }
});

const websim = {
    // Store and access student stress data
    stressData: studentStressData,
    updateStressData: updateStressData,

    chat: {
        completions: {
            create: async function(options) {
                try {
                    // Add stress data to the request if available
                    const requestBody = {
                        messages: options.messages,
                        json: options.json || false
                    };

                    // Include stress data if available
                    if (studentStressData.lastPrediction) {
                        requestBody.stressData = studentStressData;
                    }

                    const response = await fetch('http://localhost:3000/api/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody),
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`API error: ${errorData.error || response.statusText}`);
                    }

                    return await response.json();
                } catch (error) {
                    console.error('Error in websim.chat.completions.create:', error);
                    throw error;
                }
            }
        }
    }
};

console.log('Websim loaded - Using Ollama with Llama 3.2 (1B) model');
