document.addEventListener('DOMContentLoaded', () => {
    // Get DOM elements for navigation
    const navItems = document.querySelectorAll('.nav-item');
    const profileButton = document.getElementById('profileButton');
    const profileModal = document.getElementById('profileModal');
    const closeModalBtn = document.querySelector('#profileModal .close-modal');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const mainNav = document.getElementById('mainNav');
    const navToggleBtn = document.getElementById('navToggleBtn');
    const navPositionBtn = document.getElementById('navPositionBtn');
    const pageContainer = document.querySelector('.page-container');

    // Navigation position state
    let navPosition = localStorage.getItem('navPosition') || 'top';
    let navVisible = true;

    // Initialize nav position
    if (navPosition !== 'top') {
        setNavPosition(navPosition);
    }

    // Toggle navigation visibility
    navToggleBtn.addEventListener('click', () => {
        if (navPosition !== 'top') {
            mainNav.classList.toggle('active');
            navToggleBtn.classList.toggle('active');
            navVisible = !navVisible;
            updateToggleButtonPosition();
        }
    });

    // Toggle navigation position (top, left, right)
    navPositionBtn.addEventListener('click', () => {
        if (navPosition === 'top') {
            setNavPosition('left');
        } else if (navPosition === 'left') {
            setNavPosition('right');
        } else {
            setNavPosition('top');
        }
    });

    function setNavPosition(position) {
        navPosition = position;
        localStorage.setItem('navPosition', position);

        // Reset classes and styles
        mainNav.classList.remove('sidebar', 'right', 'active');
        pageContainer.classList.remove('nav-sidebar', 'right');
        navToggleBtn.style.display = 'none';
        navToggleBtn.style.left = '';
        navToggleBtn.style.right = '';
        pageContainer.style.marginLeft = '';
        pageContainer.style.marginRight = '';
        pageContainer.style.marginTop = '';
        navVisible = true;

        if (position === 'left') {
            mainNav.classList.add('sidebar');
            pageContainer.classList.add('nav-sidebar');
            navToggleBtn.style.display = 'flex';
            navToggleBtn.style.left = '10px';
            navToggleBtn.style.right = 'auto';

            // Check if mobile
            if (window.innerWidth <= 768) {
                pageContainer.style.marginTop = '60px';
            }
        } else if (position === 'right') {
            mainNav.classList.add('sidebar', 'right');
            pageContainer.classList.add('nav-sidebar', 'right');
            navToggleBtn.style.display = 'flex';
            navToggleBtn.style.left = 'auto';
            navToggleBtn.style.right = '10px';

            // Check if mobile
            if (window.innerWidth <= 768) {
                pageContainer.style.marginTop = '60px';
            }
        } else {
            // Top position - reset to default
            navToggleBtn.style.display = 'none';
        }

        // Update toggle button position based on current nav state
        updateToggleButtonPosition();
    }

    // Update toggle button position
    function updateToggleButtonPosition() {
        if (navPosition === 'left') {
            if (navVisible && window.innerWidth > 768) {
                navToggleBtn.style.left = '250px';
            } else {
                navToggleBtn.style.left = '10px';
            }
        } else if (navPosition === 'right') {
            if (navVisible && window.innerWidth > 768) {
                navToggleBtn.style.right = '250px';
            } else {
                navToggleBtn.style.right = '10px';
            }
        }
    }

    // Handle window resize for responsive behavior
    window.addEventListener('resize', () => {
        updateToggleButtonPosition();

        // Reset sidebar state on mobile/desktop transition
        if (window.innerWidth <= 768 && navPosition !== 'top') {
            // Mobile: ensure sidebar is hidden by default
            if (navVisible) {
                mainNav.classList.remove('active');
                navToggleBtn.classList.remove('active');
                navVisible = false;
            }
        } else if (window.innerWidth > 768 && navPosition !== 'top') {
            // Desktop: ensure sidebar is visible
            if (!navVisible) {
                mainNav.classList.add('active');
                navToggleBtn.classList.add('active');
                navVisible = true;
            }
        }
    });

    // Original inputs from the stress predictor
    const sleepInput = document.getElementById('sleep');
    const sleepValueDisplay = document.getElementById('sleepValue');
    const homeworkInput = document.getElementById('homework');
    const homeworkValueDisplay = document.getElementById('homeworkValue');
    const examsInput = document.getElementById('exams');
    const socialInput = document.getElementById('social');
    const socialValueDisplay = document.getElementById('socialValue');
    const extracurricularsInput = document.getElementById('extracurriculars');
    const extracurricularsValueDisplay = document.getElementById('extracurricularsValue');
    const nutritionInput = document.getElementById('nutrition');
    const nutritionValueDisplay = document.getElementById('nutritionValue');
    const screenInput = document.getElementById('screen');
    const screenValueDisplay = document.getElementById('screenValue');

    const predictButton = document.getElementById('predictButton');
    const resultArea = document.getElementById('resultArea');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const predictionOutput = document.getElementById('predictionOutput');
    const stressLevelText = document.getElementById('stressLevelText');
    const stressEmoji = document.getElementById('stressEmoji');
    const stressAdviceText = document.getElementById('stressAdviceText');
    const stressMeterFill = document.getElementById('stressMeterFill');
    const personalizedTips = document.getElementById('personalizedTips');

    const saveResultButton = document.getElementById('saveResultButton');
    const historySection = document.getElementById('historySection');
    const historyList = document.getElementById('historyList');
    const clearHistoryButton = document.getElementById('clearHistoryButton');
    const stressChart = document.getElementById('stressChart');

    // New activity page elements
    const playMusicBtn = document.getElementById('playMusicBtn');
    const musicSelect = document.getElementById('musicSelect');
    const volumeControl = document.getElementById('volume');
    const startBreathingBtn = document.getElementById('startBreathingBtn');
    const breathingCircle = document.getElementById('breathingCircle');
    const breathingInstruction = document.getElementById('breathingInstruction');
    const startStretchBtn = document.getElementById('startStretchBtn');
    const prevStretchBtn = document.getElementById('prevStretchBtn');
    const nextStretchBtn = document.getElementById('nextStretchBtn');
    const stretchTime = document.getElementById('stretchTime');
    const stretchInstruction = document.getElementById('stretchInstruction');
    const mindfulnessTimer = document.getElementById('mindfulnessTimer');
    const timeButtons = document.querySelectorAll('.time-btn');
    const startMindfulnessBtn = document.getElementById('startMindfulnessBtn');

    // Chat page elements
    const chatMessages = document.getElementById('chatMessages');
    const userMessageInput = document.getElementById('userMessageInput');
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    const suggestionChips = document.querySelectorAll('.suggestion-chip');

    // Patch: Fix Chat Page display!
    // Instead of trying to query '.page', directly select the three main page containers for reliability
    const mainPage = document.getElementById('mainPage');
    const activitiesPage = document.getElementById('activitiesPage');
    const aiChatPage = document.getElementById('aiChatPage');

    // Load user profile data
    let userProfile = JSON.parse(localStorage.getItem('userProfile')) || {
        name: '',
        age: '',
        grade: '',
        major: '',
        stressors: [],
        avatarUrl: 'https://via.placeholder.com/150'
    };

    // Update UI with profile data
    function updateProfileUI() {
        document.getElementById('profileName').value = userProfile.name;
        document.getElementById('profileAge').value = userProfile.age;
        document.getElementById('profileGrade').value = userProfile.grade;
        document.getElementById('profileMajor').value = userProfile.major;

        // Reset checkboxes
        document.querySelectorAll('input[name="stressor"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Check the user's stressors
        userProfile.stressors.forEach(stressor => {
            const checkbox = document.querySelector(`input[name="stressor"][value="${stressor}"]`);
            if (checkbox) checkbox.checked = true;
        });

        // Update avatar
        document.getElementById('profileAvatarPreview').src = userProfile.avatarUrl;
        document.getElementById('profileAvatar').src = userProfile.avatarUrl;

        // Update welcome message
        updateWelcomeMessage();

        // Update greeting text on main page
        updateGreetingText();
    }

    // Update greeting text based on username
    function updateGreetingText() {
        const greetingText = document.getElementById('greetingText');
        const name = userProfile.name || 'you';
        let greeting = '';

        const currentHour = new Date().getHours();
        if (currentHour < 12) {
            greeting = `Good morning, ${name}! How are you feeling today?`;
        } else if (currentHour < 18) {
            greeting = `Good afternoon, ${name}! How are you feeling today?`;
        } else {
            greeting = `Good evening, ${name}! How are you feeling today?`;
        }

        greetingText.textContent = greeting;
    }

    // Welcome message
    const welcomeMessage = document.getElementById('welcomeMessage');

    // Update welcome message based on username
    function updateWelcomeMessage() {
        const name = userProfile.name || 'Student';
        welcomeMessage.innerHTML = `
            <p>Hello, ${name}! Welcome to your advanced AI assistant powered by cutting-edge language models.
            I can help with complex questions, provide detailed explanations, and assist with a wide range of topics.</p>
        `;
    }

    // Initialize stress history data
    let stressHistory = JSON.parse(localStorage.getItem('stressHistory')) || [];
    let stressChartInstance = null;

    // Conversation memory for AI chat
    let conversationHistory = [{
        role: "assistant",
        content: "Hello! I'm your advanced AI assistant powered by state-of-the-art language models. I can help with complex questions, provide detailed explanations, generate code, and assist with a wide range of topics. How can I assist you today?"
    }];
    let lastStressPrediction = null;

    // Chat history management
    let chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
    let currentChatId = null;
    let selectedChatId = null;

    // Helper: Adds message to chat view with text effects
    function addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;

        // Add avatar
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';

        // If user has a profile avatar and it's a user message, use it
        if (role === 'user' && userProfile && userProfile.avatarUrl) {
            avatarDiv.style.backgroundImage = `url('${userProfile.avatarUrl}')`;
        }

        messageDiv.appendChild(avatarDiv);

        // Create message bubble
        const messageBubble = document.createElement('div');
        messageBubble.className = 'message-bubble';

        // Create message content inside bubble
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        // Add timestamp
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        const now = new Date();
        timeDiv.textContent = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        messageBubble.appendChild(timeDiv);

        // Apply text effects for AI messages
        if (role === 'ai') {
            // Process content for special formatting
            const formattedContent = processContentForEffects(content);

            // For AI messages, we'll add a simple fade-in effect
            messageContent.innerHTML = formattedContent;
            messageContent.style.opacity = '0';
            messageBubble.appendChild(messageContent);
            messageDiv.appendChild(messageBubble);
            chatMessages.appendChild(messageDiv);

            // Fade in the message
            setTimeout(() => {
                // Use a CSS transition for smooth fade-in
                messageContent.style.transition = 'opacity 0.5s ease';
                messageContent.style.opacity = '1';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }, 100);
        } else {
            // User messages appear instantly
            // Escape HTML for user messages to prevent injection
            const escapedContent = document.createElement('div');
            escapedContent.textContent = content;
            messageContent.innerHTML = `<p>${escapedContent.innerHTML}</p>`;
            messageBubble.appendChild(messageContent);
            messageDiv.appendChild(messageBubble);
            chatMessages.appendChild(messageDiv);
        }

        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Process content to add visual effects
    function processContentForEffects(content) {
        // Create a temporary div to safely parse content
        const tempDiv = document.createElement('div');
        tempDiv.textContent = content;
        content = tempDiv.innerHTML;

        // Only apply effects to plain text, not HTML
        // Handle emojis - only match standalone emojis, not digits
        content = content.replace(/(\p{Emoji}+)/gu, (match) => {
            // Skip if it's a digit
            if (/^\d+$/.test(match)) return match;
            return `<span class="large-emoji">${match}</span>`;
        });

        // Highlight important phrases
        const highlightPhrases = [
            'stress', 'anxiety', 'relax', 'breathe', 'sleep', 'exercise',
            'important', 'remember', 'focus', 'break', 'rest', 'study'
        ];

        // Use a more careful approach to avoid replacing inside HTML tags
        highlightPhrases.forEach(phrase => {
            const regex = new RegExp(`\\b${phrase}\\b`, 'gi');
            content = content.replace(regex, `<span class="highlight-text">$&</span>`);
        });

        // Add emphasis to advice sections
        content = content.replace(/(Tip|Advice|Suggestion|Recommendation):/g,
            '<span class="advice-heading">$1:</span>');

        // Format lists with better styling - only at start of line
        content = content.replace(/^(\s*[-*]\s+)(.+)$/gm,
            '<span class="list-item">• $2</span>');

        // Wrap content in paragraph for proper display
        return `<p>${content}</p>`;
    }

    // Helper: initial chat state and welcome
    function initializeChat() {
        chatMessages.innerHTML = '';

        // Get stress level info if available
        let welcomeMessage = "Hello! I'm your advanced AI assistant powered by state-of-the-art language models. I can help with complex questions, provide detailed explanations, generate code, and assist with a wide range of topics. How can I assist you today?";

        // Add personalized welcome if we have user profile
        if (userProfile && userProfile.name) {
            welcomeMessage = `Hello ${userProfile.name}! I'm your advanced AI assistant powered by state-of-the-art language models. I can help with complex questions, provide detailed explanations, generate code, and assist with a wide range of topics. How can I assist you today?`;
        }

        // Add stress level info if available
        if (lastStressPrediction) {
            welcomeMessage += ` I notice your current stress level is ${lastStressPrediction.level} (${lastStressPrediction.score}/100). Would you like some advanced techniques for managing that?`;
        }

        addMessage('ai', welcomeMessage);
    }

    // *** Fix navigation logic for Chat Page visibility ***
    navItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const targetPage = item.getAttribute('data-page');
            // Update active nav item
            navItems.forEach(nav => nav.classList.remove('active'));
            item.classList.add('active');

            // Show selected page, hide others
            // Instead of looping all '.page', explicitly show the right container for better reliability
            if (targetPage === 'main') {
                mainPage.classList.remove('hidden');
                activitiesPage.classList.add('hidden');
                aiChatPage.classList.add('hidden');
            } else if (targetPage === 'activities') {
                mainPage.classList.add('hidden');
                activitiesPage.classList.remove('hidden');
                aiChatPage.classList.add('hidden');
            } else if (targetPage === 'ai-chat') {
                mainPage.classList.add('hidden');
                activitiesPage.classList.add('hidden');
                aiChatPage.classList.remove('hidden');
                initializeChat();
            }
            // Close mobile nav if open
            if (navPosition !== 'top' && !navVisible) {
                mainNav.classList.remove('active');
                navToggleBtn.classList.remove('active');
                navVisible = true;
            }
        });
    });

    // AI Response handler
    async function getAIChatResponse(message) {
        // Use user's name if set
        const profileName = (userProfile && userProfile.name) ? userProfile.name : 'student';
        // Compose a helpful system prompt
        let basePrompt = "You are a kind, concise, and positive AI that helps students with stress, studying, and wellness.";
        if (lastStressPrediction) {
            basePrompt += ` The user recently ran a stress prediction. Their stress level was "${lastStressPrediction.level}", score: ${lastStressPrediction.score}, with advice: "${lastStressPrediction.advice}". Factor this recent result into your responses if relevant.`;
        }
        basePrompt += " If the user's input mentions stress, context, their name, or study life, reply with specific supportive suggestions.";
        basePrompt += " Keep responses short, friendly, actionable, and supportive.";
        // Add their name to personalize
        basePrompt += ` Address the user as "${profileName}".`;

        conversationHistory.push({
            role: "user",
            content: message
        });
        const history = conversationHistory.slice(-10);

        // Show thinking / typing indicator with avatar
        const thinkingDiv = document.createElement('div');
        thinkingDiv.className = 'message ai-message thinking';

        // Add avatar
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        thinkingDiv.appendChild(avatarDiv);

        // Add message bubble with typing animation
        const messageBubble = document.createElement('div');
        messageBubble.className = 'message-bubble';

        // Add typing indicator
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = `
            <span></span>
            <span></span>
            <span></span>
        `;

        messageBubble.appendChild(typingIndicator);
        thinkingDiv.appendChild(messageBubble);
        chatMessages.appendChild(thinkingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    { role: "system", content: basePrompt },
                    ...history
                ],
            });

            // Add response to conversation and display
            conversationHistory.push({
                role: "assistant",
                content: completion.content
            });
            thinkingDiv.remove();
            addMessage('ai', completion.content);

            // Auto-save chat if it has a current ID
            if (currentChatId) {
                // Find the chat in history
                const chatIndex = chatHistory.findIndex(chat => chat.id === currentChatId);
                if (chatIndex !== -1) {
                    // Update the chat
                    chatHistory[chatIndex].messages = [...conversationHistory];
                    chatHistory[chatIndex].messageCount = conversationHistory.length;

                    // Save to localStorage
                    localStorage.setItem('chatHistory', JSON.stringify(chatHistory));
                }
            }
        } catch (e) {
            thinkingDiv.remove();
            addMessage('ai', "Sorry, I couldn't process your request right now. Please try again.");
        }
    }

    // Chat send on button click
    sendMessageBtn.addEventListener('click', () => {
        const message = userMessageInput.value.trim();
        if (!message) return;
        addMessage('user', message);
        getAIChatResponse(message);
        userMessageInput.value = '';
    });

    // Chat send on enter keypress in textarea
    userMessageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const message = userMessageInput.value.trim();
            if (!message) return;
            addMessage('user', message);
            getAIChatResponse(message);
            userMessageInput.value = '';
        }
    });

    // Suggestion buttons for chat
    suggestionChips.forEach(chip => {
        chip.addEventListener('click', () => {
            const message = chip.getAttribute('data-message');
            userMessageInput.value = message;
            userMessageInput.focus();
        });
    });

    // Chat history modal elements
    const chatHistoryModal = document.getElementById('chatHistoryModal');
    const closeChatHistoryBtn = document.getElementById('closeChatHistoryBtn');
    const chatHistoryList = document.getElementById('chatHistoryList');
    const chatHistoryPreview = document.getElementById('chatHistoryPreview');
    const chatHistorySearch = document.getElementById('chatHistorySearch');
    const searchChatHistoryBtn = document.getElementById('searchChatHistoryBtn');
    const loadSelectedChatBtn = document.getElementById('loadSelectedChatBtn');
    const deleteSelectedChatBtn = document.getElementById('deleteSelectedChatBtn');
    const exportSelectedChatBtn = document.getElementById('exportSelectedChatBtn');
    const deleteAllChatsBtn = document.getElementById('deleteAllChatsBtn');

    // Chat action buttons
    const saveChatBtn = document.getElementById('saveChatBtn');
    const historyBtn = document.getElementById('historyBtn');
    const exportChatBtn = document.getElementById('exportChatBtn');
    const clearChatBtn = document.getElementById('clearChatBtn');
    const chatSaveStatus = document.getElementById('chatSaveStatus');

    // Save current chat
    saveChatBtn.addEventListener('click', () => {
        if (conversationHistory.length <= 1) {
            showSaveStatus('No conversation to save', false);
            return;
        }

        // Create a title from the first user message
        const firstUserMessage = conversationHistory.find(msg => msg.role === 'user');
        let chatTitle = firstUserMessage ? firstUserMessage.content : 'New Chat';

        // Truncate title if too long
        if (chatTitle.length > 50) {
            chatTitle = chatTitle.substring(0, 47) + '...';
        }

        // Create a new chat object
        const newChat = {
            id: currentChatId || generateChatId(),
            title: chatTitle,
            date: new Date().toISOString(),
            messages: [...conversationHistory],
            messageCount: conversationHistory.length
        };

        // Update or add to chat history
        if (currentChatId) {
            // Update existing chat
            const index = chatHistory.findIndex(chat => chat.id === currentChatId);
            if (index !== -1) {
                chatHistory[index] = newChat;
            } else {
                chatHistory.push(newChat);
            }
        } else {
            // Add new chat
            chatHistory.push(newChat);
            currentChatId = newChat.id;
        }

        // Save to localStorage
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

        // Show save confirmation
        showSaveStatus('Chat saved successfully!', true);
    });

    // Show chat history modal
    historyBtn.addEventListener('click', () => {
        updateChatHistoryList();
        chatHistoryModal.classList.add('show');
    });

    // Export current chat
    exportChatBtn.addEventListener('click', () => {
        if (conversationHistory.length <= 1) {
            showSaveStatus('No conversation to export', false);
            return;
        }

        exportChat(conversationHistory);
    });

    // Clear current chat
    clearChatBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to clear the current chat?')) {
            // Reset conversation history to initial state
            conversationHistory = [{
                role: "assistant",
                content: "Hello! I'm your advanced AI assistant powered by state-of-the-art language models. I can help with complex questions, provide detailed explanations, generate code, and assist with a wide range of topics. How can I assist you today?"
            }];

            // Reset current chat ID
            currentChatId = null;

            // Clear chat messages display
            initializeChat();

            showSaveStatus('Chat cleared', true);
        }
    });

    // Close chat history modal
    closeChatHistoryBtn.addEventListener('click', () => {
        chatHistoryModal.classList.remove('show');
    });

    // Search chat history
    searchChatHistoryBtn.addEventListener('click', () => {
        const searchTerm = chatHistorySearch.value.trim().toLowerCase();
        if (!searchTerm) {
            updateChatHistoryList();
            return;
        }

        // Filter chats by search term
        const filteredChats = chatHistory.filter(chat => {
            // Search in title
            if (chat.title.toLowerCase().includes(searchTerm)) {
                return true;
            }

            // Search in messages
            return chat.messages.some(msg =>
                msg.content.toLowerCase().includes(searchTerm)
            );
        });

        updateChatHistoryList(filteredChats);
    });

    // Load selected chat
    loadSelectedChatBtn.addEventListener('click', () => {
        if (!selectedChatId) return;

        const selectedChat = chatHistory.find(chat => chat.id === selectedChatId);
        if (selectedChat) {
            // Load conversation history
            conversationHistory = [...selectedChat.messages];
            currentChatId = selectedChat.id;

            // Update chat display
            chatMessages.innerHTML = '';
            conversationHistory.forEach(msg => {
                if (msg.role !== 'system') {
                    addMessage(msg.role, msg.content);
                }
            });

            // Close modal
            chatHistoryModal.classList.remove('show');

            showSaveStatus('Chat loaded successfully', true);
        }
    });

    // Delete selected chat
    deleteSelectedChatBtn.addEventListener('click', () => {
        if (!selectedChatId) return;

        if (confirm('Are you sure you want to delete this chat?')) {
            // Remove from chat history
            chatHistory = chatHistory.filter(chat => chat.id !== selectedChatId);

            // Save to localStorage
            localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

            // Reset selected chat
            selectedChatId = null;

            // Update chat history list
            updateChatHistoryList();

            // Disable buttons
            loadSelectedChatBtn.disabled = true;
            deleteSelectedChatBtn.disabled = true;
            exportSelectedChatBtn.disabled = true;

            // Clear preview
            chatHistoryPreview.innerHTML = `
                <div class="preview-placeholder">
                    <p>Select a chat to preview</p>
                </div>
            `;
        }
    });

    // Export selected chat
    exportSelectedChatBtn.addEventListener('click', () => {
        if (!selectedChatId) return;

        const selectedChat = chatHistory.find(chat => chat.id === selectedChatId);
        if (selectedChat) {
            exportChat(selectedChat.messages, selectedChat.title);
        }
    });

    // Delete all chats
    deleteAllChatsBtn.addEventListener('click', () => {
        if (chatHistory.length === 0) return;

        if (confirm('Are you sure you want to delete ALL saved chats? This cannot be undone.')) {
            // Clear chat history
            chatHistory = [];

            // Save to localStorage
            localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

            // Reset selected chat
            selectedChatId = null;

            // Update chat history list
            updateChatHistoryList();

            // Disable buttons
            loadSelectedChatBtn.disabled = true;
            deleteSelectedChatBtn.disabled = true;
            exportSelectedChatBtn.disabled = true;

            // Clear preview
            chatHistoryPreview.innerHTML = `
                <div class="preview-placeholder">
                    <p>Select a chat to preview</p>
                </div>
            `;
        }
    });

    // Update chat history list
    function updateChatHistoryList(chats = chatHistory) {
        chatHistoryList.innerHTML = '';

        if (chats.length === 0) {
            chatHistoryList.innerHTML = `<div class="empty-history-message">No saved chats yet</div>`;
            return;
        }

        // Sort chats by date (newest first)
        const sortedChats = [...chats].sort((a, b) =>
            new Date(b.date) - new Date(a.date)
        );

        sortedChats.forEach(chat => {
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-history-item';
            chatItem.dataset.chatId = chat.id;

            if (chat.id === selectedChatId) {
                chatItem.classList.add('selected');
            }

            // Format date
            const chatDate = new Date(chat.date);
            const formattedDate = chatDate.toLocaleDateString() + ' ' +
                                 chatDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            // Get first user message for preview
            const firstUserMsg = chat.messages.find(msg => msg.role === 'user');
            const previewText = firstUserMsg ? firstUserMsg.content : 'No messages';

            chatItem.innerHTML = `
                <div class="chat-history-item-header">
                    <div class="chat-history-item-title">${chat.title}</div>
                    <div class="chat-history-item-date">${formattedDate}</div>
                </div>
                <div class="chat-history-item-preview">${previewText}</div>
                <div class="chat-history-item-meta">
                    <span>${chat.messageCount} messages</span>
                </div>
            `;

            chatItem.addEventListener('click', () => {
                // Deselect all items
                document.querySelectorAll('.chat-history-item').forEach(item => {
                    item.classList.remove('selected');
                });

                // Select this item
                chatItem.classList.add('selected');
                selectedChatId = chat.id;

                // Enable buttons
                loadSelectedChatBtn.disabled = false;
                deleteSelectedChatBtn.disabled = false;
                exportSelectedChatBtn.disabled = false;

                // Show preview
                showChatPreview(chat);
            });

            chatHistoryList.appendChild(chatItem);
        });
    }

    // Show chat preview
    function showChatPreview(chat) {
        chatHistoryPreview.innerHTML = '';

        // Show only a subset of messages for preview (max 10)
        const previewMessages = chat.messages.slice(0, 10);

        previewMessages.forEach(msg => {
            if (msg.role === 'system') return;

            const messageDiv = document.createElement('div');
            messageDiv.className = `preview-message ${msg.role}`;

            const messageHeader = document.createElement('div');
            messageHeader.className = 'preview-message-header';
            messageHeader.textContent = msg.role === 'assistant' ? 'AI' : 'You';

            const messageContent = document.createElement('div');
            messageContent.className = 'preview-message-content';
            messageContent.textContent = msg.content;

            messageDiv.appendChild(messageHeader);
            messageDiv.appendChild(messageContent);
            chatHistoryPreview.appendChild(messageDiv);
        });

        // Add message count indicator if there are more messages
        if (chat.messages.length > 10) {
            const moreMessages = document.createElement('div');
            moreMessages.className = 'preview-more-messages';
            moreMessages.textContent = `+ ${chat.messages.length - 10} more messages`;
            chatHistoryPreview.appendChild(moreMessages);
        }
    }

    // Generate a unique ID for chats
    function generateChatId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    // Show save status message
    function showSaveStatus(message, isSuccess) {
        chatSaveStatus.textContent = message;
        chatSaveStatus.style.color = isSuccess ? '#2ecc71' : '#e74c3c';
        chatSaveStatus.classList.add('show');

        setTimeout(() => {
            chatSaveStatus.classList.remove('show');
        }, 3000);
    }

    // Export chat as JSON file
    function exportChat(messages, title = 'chat-export') {
        // Create export object
        const exportData = {
            title: title,
            date: new Date().toISOString(),
            messages: messages
        };

        // Convert to JSON
        const jsonData = JSON.stringify(exportData, null, 2);

        // Create blob and download link
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        // Create download link
        const a = document.createElement('a');
        a.href = url;
        a.download = `${title.replace(/[^a-z0-9]/gi, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 100);
    }

    // Add stress level suggestion chip if not present
    const suggestionsDiv = document.querySelector('.suggestion-chips');
    if (!document.querySelector('.suggestion-chip[data-message="Help me with my current stress level"]')) {
        const stressChip = document.createElement('button');
        stressChip.className = 'suggestion-chip';
        stressChip.setAttribute('data-message', 'Help me with my current stress level');
        stressChip.textContent = 'My stress level';
        stressChip.addEventListener('click', () => {
            userMessageInput.value = 'Help me with my current stress level';
            userMessageInput.focus();
        });
        suggestionsDiv.appendChild(stressChip);
    }

    // AI Music Player
    let aiAudioElement;
    let isAiPlaying = false;
    let aiProgressInterval;
    let aiCurrentTime = 0;
    let aiTotalDuration = 0;

    // AI Music elements
    const aiMusicMood = document.getElementById('aiMusicMood');
    const aiMusicStyle = document.getElementById('aiMusicStyle');
    const aiMusicDuration = document.getElementById('aiMusicDuration');
    const generateAiMusicBtn = document.getElementById('generateAiMusicBtn');
    const aiGenerating = document.querySelector('.ai-generating');
    const aiTrackPlayer = document.querySelector('.ai-track-player');
    const aiTrackName = document.getElementById('aiTrackName');
    const aiTrackDescription = document.getElementById('aiTrackDescription');
    const playAiMusicBtn = document.getElementById('playAiMusicBtn');
    const aiMusicProgressFill = document.getElementById('aiMusicProgressFill');
    const aiMusicTime = document.getElementById('aiMusicTime');
    const aiVolume = document.getElementById('aiVolume');

    // AI Music tracks - Using data URLs for guaranteed compatibility
    // These will fallback to synthetic audio if needed
    const aiMusicTracks = {
        // We'll primarily use synthetic audio for reliability
        calm_ambient: null,
        calm_lofi: null,
        calm_classical: null,
        calm_nature: null,

        focus_ambient: null,
        focus_lofi: null,
        focus_classical: null,
        focus_nature: null,

        uplifting_ambient: null,
        uplifting_lofi: null,
        uplifting_classical: null,
        uplifting_nature: null,

        sleep_ambient: null,
        sleep_lofi: null,
        sleep_classical: null,
        sleep_nature: null
    };

    // Backup: Generate synthetic relaxing sounds using Web Audio API
    let audioContext;
    let oscillators = [];
    let gainNodes = [];

    // Initialize Web Audio API
    function initAudioContext() {
        if (!audioContext) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        return audioContext;
    }

    // Generate synthetic relaxation beats with varied wavelengths and instruments
    function generateSyntheticAmbient(mood, style, duration) {
        const ctx = initAudioContext();

        // Create different beat profiles based on mood and style
        const beatProfiles = {
            calm_ambient: {
                baseFreqs: [110, 220, 330],
                beatPattern: [60, 80], // BPM variations
                instruments: ['pad', 'bell', 'drone'],
                harmonics: [1, 2, 3, 5]
            },
            calm_nature: {
                baseFreqs: [80, 160, 240],
                beatPattern: [50, 70],
                instruments: ['wind', 'water', 'bird'],
                harmonics: [1, 1.5, 2, 3]
            },
            calm_lofi: {
                baseFreqs: [90, 180, 270],
                beatPattern: [70, 90],
                instruments: ['vinyl', 'piano', 'bass'],
                harmonics: [1, 2, 4, 6]
            },
            calm_classical: {
                baseFreqs: [130.81, 261.63, 392.00], // C3, C4, G4
                beatPattern: [60, 80],
                instruments: ['string', 'flute', 'harp'],
                harmonics: [1, 2, 3, 4, 5]
            },
            focus_ambient: {
                baseFreqs: [100, 200, 400],
                beatPattern: [80, 100],
                instruments: ['pulse', 'sweep', 'drone'],
                harmonics: [1, 2, 4, 8]
            },
            focus_lofi: {
                baseFreqs: [110, 220, 440],
                beatPattern: [90, 110],
                instruments: ['beat', 'synth', 'pad'],
                harmonics: [1, 2, 3, 6]
            },
            sleep_ambient: {
                baseFreqs: [60, 120, 180],
                beatPattern: [40, 60],
                instruments: ['breath', 'wave', 'hum'],
                harmonics: [1, 1.5, 2]
            }
        };

        const profile = beatProfiles[`${mood}_${style}`] || beatProfiles['calm_ambient'];

        // Clear existing oscillators
        stopSyntheticAudio();

        // Create complex layered soundscape
        createLayeredSoundscape(ctx, profile, duration);

        console.log('🎵 Generated relaxation beats with varied wavelengths');
        return true;
    }

    // Create a layered soundscape with multiple instruments and changing patterns
    function createLayeredSoundscape(ctx, profile, duration) {
        const startTime = ctx.currentTime;

        // Layer 1: Base drone/pad
        createBaseDrone(ctx, profile, startTime);

        // Layer 2: Rhythmic elements
        createRhythmicLayer(ctx, profile, startTime);

        // Layer 3: Melodic elements
        createMelodicLayer(ctx, profile, startTime);

        // Layer 4: Ambient textures
        createAmbientTextures(ctx, profile, startTime);
    }

    // Create base drone layer
    function createBaseDrone(ctx, profile, startTime) {
        profile.baseFreqs.forEach((baseFreq, index) => {
            const oscillator = ctx.createOscillator();
            const gainNode = ctx.createGain();
            const filterNode = ctx.createBiquadFilter();

            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(baseFreq, startTime);

            // Slow frequency drift
            oscillator.frequency.linearRampToValueAtTime(baseFreq * 1.02, startTime + 30);
            oscillator.frequency.linearRampToValueAtTime(baseFreq * 0.98, startTime + 60);

            filterNode.type = 'lowpass';
            filterNode.frequency.setValueAtTime(400 + index * 200, startTime);
            filterNode.Q.setValueAtTime(0.3, startTime);

            gainNode.gain.setValueAtTime(0, startTime);
            gainNode.gain.linearRampToValueAtTime(0.03 + index * 0.01, startTime + 4);

            oscillator.connect(filterNode);
            filterNode.connect(gainNode);
            gainNode.connect(ctx.destination);

            oscillator.start(startTime);

            oscillators.push(oscillator);
            gainNodes.push(gainNode);
        });
    }

    // Create rhythmic layer with changing patterns
    function createRhythmicLayer(ctx, profile, startTime) {
        const beatInterval = 60 / profile.beatPattern[0]; // Convert BPM to seconds

        for (let i = 0; i < 8; i++) {
            setTimeout(() => {
                if (oscillators.length === 0) return; // Check if stopped

                const freq = profile.baseFreqs[1] * (1 + Math.random() * 0.1);
                const osc = ctx.createOscillator();
                const gain = ctx.createGain();
                const filter = ctx.createBiquadFilter();

                osc.type = 'triangle';
                osc.frequency.setValueAtTime(freq, ctx.currentTime);

                filter.type = 'bandpass';
                filter.frequency.setValueAtTime(freq * 2, ctx.currentTime);
                filter.Q.setValueAtTime(2, ctx.currentTime);

                gain.gain.setValueAtTime(0, ctx.currentTime);
                gain.gain.linearRampToValueAtTime(0.02, ctx.currentTime + 0.1);
                gain.gain.exponentialRampToValueAtTime(0.001, ctx.currentTime + 0.5);

                osc.connect(filter);
                filter.connect(gain);
                gain.connect(ctx.destination);

                osc.start(ctx.currentTime);
                osc.stop(ctx.currentTime + 0.5);

            }, i * beatInterval * 1000);
        }
    }

    // Create melodic layer with harmonic variations
    function createMelodicLayer(ctx, profile, startTime) {
        profile.harmonics.forEach((harmonic, index) => {
            setTimeout(() => {
                if (oscillators.length === 0) return;

                const freq = profile.baseFreqs[0] * harmonic;
                const osc = ctx.createOscillator();
                const gain = ctx.createGain();
                const delay = ctx.createDelay();
                const delayGain = ctx.createGain();

                osc.type = index % 2 === 0 ? 'sine' : 'triangle';
                osc.frequency.setValueAtTime(freq, ctx.currentTime);

                // Add subtle vibrato
                const vibrato = ctx.createOscillator();
                const vibratoGain = ctx.createGain();
                vibrato.frequency.setValueAtTime(4 + Math.random() * 2, ctx.currentTime);
                vibratoGain.gain.setValueAtTime(freq * 0.01, ctx.currentTime);
                vibrato.connect(vibratoGain);
                vibratoGain.connect(osc.frequency);
                vibrato.start(ctx.currentTime);

                // Add delay effect
                delay.delayTime.setValueAtTime(0.3 + Math.random() * 0.2, ctx.currentTime);
                delayGain.gain.setValueAtTime(0.2, ctx.currentTime);

                gain.gain.setValueAtTime(0, ctx.currentTime);
                gain.gain.linearRampToValueAtTime(0.015, ctx.currentTime + 2);
                gain.gain.linearRampToValueAtTime(0.01, ctx.currentTime + 8);

                osc.connect(gain);
                gain.connect(ctx.destination);
                gain.connect(delay);
                delay.connect(delayGain);
                delayGain.connect(ctx.destination);

                osc.start(ctx.currentTime);

                oscillators.push(osc);
                oscillators.push(vibrato);
                gainNodes.push(gain);

            }, index * 2000);
        });
    }

    // Create ambient textures and noise
    function createAmbientTextures(ctx, profile, startTime) {
        // Create filtered noise for texture
        const bufferSize = ctx.sampleRate * 2;
        const noiseBuffer = ctx.createBuffer(1, bufferSize, ctx.sampleRate);
        const output = noiseBuffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            output[i] = Math.random() * 2 - 1;
        }

        const noiseSource = ctx.createBufferSource();
        const noiseGain = ctx.createGain();
        const noiseFilter = ctx.createBiquadFilter();

        noiseSource.buffer = noiseBuffer;
        noiseSource.loop = true;

        noiseFilter.type = 'lowpass';
        noiseFilter.frequency.setValueAtTime(200, startTime);
        noiseFilter.Q.setValueAtTime(0.5, startTime);

        noiseGain.gain.setValueAtTime(0, startTime);
        noiseGain.gain.linearRampToValueAtTime(0.005, startTime + 5);

        noiseSource.connect(noiseFilter);
        noiseFilter.connect(noiseGain);
        noiseGain.connect(ctx.destination);

        noiseSource.start(startTime);

        oscillators.push(noiseSource);
        gainNodes.push(noiseGain);
    }

    // Stop synthetic audio
    function stopSyntheticAudio() {
        oscillators.forEach(osc => {
            try {
                osc.stop();
            } catch (e) {
                // Oscillator might already be stopped
            }
        });
        gainNodes.forEach(gain => {
            try {
                gain.disconnect();
            } catch (e) {
                // Gain node might already be disconnected
            }
        });
        oscillators = [];
        gainNodes = [];
    }

    // Cleanup function for page unload
    function cleanupAudio() {
        stopAiMusic();
        if (audioContext && audioContext.state !== 'closed') {
            audioContext.close();
        }
    }

    // Add cleanup listener
    window.addEventListener('beforeunload', cleanupAudio);

    // AI Music Generation
    generateAiMusicBtn.addEventListener('click', async () => {
        // Request permission for audio playback
        if (!await requestAudioPermission()) {
            alert('Audio permission is required to play relaxing music. Please allow audio access.');
            return;
        }

        // Show generating animation
        aiGenerating.classList.remove('hidden');
        aiTrackPlayer.classList.add('hidden');

        // Get user preferences
        const mood = aiMusicMood.value;
        const style = aiMusicStyle.value;
        const duration = aiMusicDuration.value;

        // Simulate AI generation time
        setTimeout(async () => {
            try {
                // Generate AI music track
                await generateAiTrack(mood, style, duration);

                // Hide generating animation, show player
                aiGenerating.classList.add('hidden');
                aiTrackPlayer.classList.remove('hidden');
            } catch (error) {
                console.error('Error generating AI music:', error);
                aiGenerating.classList.add('hidden');
                alert('Failed to generate music. Please try again.');
            }
        }, 2500); // Simulate 2.5 seconds of "AI generation"
    });

    // Request audio permission
    async function requestAudioPermission() {
        try {
            // Try to initialize audio context (this requires user interaction)
            const ctx = initAudioContext();
            if (ctx.state === 'suspended') {
                await ctx.resume();
            }
            return true;
        } catch (error) {
            console.error('Audio permission denied:', error);
            return false;
        }
    }

    // Play AI Music
    playAiMusicBtn.addEventListener('click', () => {
        if (isAiPlaying) {
            stopAiMusic();
        } else {
            playAiMusic();
        }
    });

    // AI Volume control
    aiVolume.addEventListener('input', () => {
        const volume = aiVolume.value / 100;

        if (aiAudioElement) {
            aiAudioElement.volume = volume;
        }

        // Control synthetic audio volume
        if (gainNodes.length > 0) {
            const ctx = audioContext;
            if (ctx) {
                gainNodes.forEach((gainNode, index) => {
                    const baseVolume = [0.1, 0.08, 0.06][index] || 0.05;
                    gainNode.gain.setValueAtTime(baseVolume * volume, ctx.currentTime);
                });
            }
        }
    });

    // Generate AI track based on preferences
    async function generateAiTrack(mood, style, duration) {
        // Create track key from mood and style
        const trackKey = `${mood}_${style}`;

        // Set duration based on selection
        let durationMinutes;
        switch(duration) {
            case 'short':
                durationMinutes = '2-3';
                aiTotalDuration = 150; // 2.5 minutes in seconds
                break;
            case 'long':
                durationMinutes = '10-15';
                aiTotalDuration = 750; // 12.5 minutes in seconds
                break;
            case 'medium':
            default:
                durationMinutes = '5-7';
                aiTotalDuration = 360; // 6 minutes in seconds
        }

        // Generate track name and description
        const trackName = generateTrackName(mood, style);
        const trackDescription = generateTrackDescription(mood, style, durationMinutes);

        // Update UI
        aiTrackName.textContent = trackName;
        aiTrackDescription.textContent = trackDescription;

        // Use synthetic audio for guaranteed compatibility and functionality
        if (aiAudioElement) {
            aiAudioElement.pause();
            aiAudioElement = null;
        }

        // Generate synthetic ambient sound
        generateSyntheticAmbient(mood, style, duration);

        // Update description to indicate AI-generated audio
        aiTrackDescription.textContent = `${durationMinutes} minute AI-generated ${style} soundscape designed for ${mood} mood`;

        console.log('🎵 Generated AI relaxation music using synthetic audio');

        // Reset progress
        aiCurrentTime = 0;
        updateAiProgress();
    }

    // Play AI music
    async function playAiMusic() {
        try {
            if (aiAudioElement) {
                // Playing regular audio file
                await aiAudioElement.play();
                console.log('🎵 Playing audio file');
            } else if (oscillators.length > 0) {
                // Already playing synthetic audio
                console.log('🎵 Synthetic audio already playing');
            } else {
                // No audio available
                console.log('❌ No audio available to play');
                return;
            }

            playAiMusicBtn.textContent = 'Stop';
            playAiMusicBtn.classList.add('playing');
            aiTrackPlayer.classList.add('playing');
            isAiPlaying = true;

            // Start progress tracking
            aiProgressInterval = setInterval(() => {
                aiCurrentTime += 1;
                if (aiCurrentTime > aiTotalDuration) {
                    aiCurrentTime = 0;
                }
                updateAiProgress();
            }, 1000);

        } catch (error) {
            console.error('Error playing AI music:', error);
            alert('Unable to play music. Please check your audio settings.');
        }
    }

    // Stop AI music
    function stopAiMusic() {
        try {
            if (aiAudioElement) {
                // Stop regular audio file
                aiAudioElement.pause();
                aiAudioElement.currentTime = 0;
                console.log('⏹️ Stopped audio file');
            }

            if (oscillators.length > 0) {
                // Stop synthetic audio
                stopSyntheticAudio();
                console.log('⏹️ Stopped synthetic audio');
            }

            playAiMusicBtn.textContent = 'Play';
            playAiMusicBtn.classList.remove('playing');
            aiTrackPlayer.classList.remove('playing');
            isAiPlaying = false;

            // Stop progress tracking
            clearInterval(aiProgressInterval);

            // Reset progress
            aiCurrentTime = 0;
            updateAiProgress();

        } catch (error) {
            console.error('Error stopping AI music:', error);
        }
    }

    // Update AI music progress display
    function updateAiProgress() {
        // Calculate percentage
        const percentage = (aiCurrentTime / aiTotalDuration) * 100;
        aiMusicProgressFill.style.width = `${percentage}%`;

        // Format time display
        const minutes = Math.floor(aiCurrentTime / 60);
        const seconds = aiCurrentTime % 60;
        aiMusicTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Generate a track name based on mood and style
    function generateTrackName(mood, style) {
        const moodNames = {
            calm: ['Tranquil', 'Serene', 'Peaceful', 'Gentle'],
            focus: ['Clarity', 'Concentration', 'Flow State', 'Deep Focus'],
            uplifting: ['Radiance', 'Elevation', 'Inspiration', 'Positivity'],
            sleep: ['Dreamscape', 'Night Journey', 'Slumber', 'Twilight']
        };

        const styleNames = {
            ambient: ['Atmosphere', 'Horizon', 'Spaces', 'Echoes'],
            lofi: ['Beats', 'Waves', 'Rhythms', 'Melodies'],
            classical: ['Sonata', 'Harmony', 'Symphony', 'Composition'],
            nature: ['Forest', 'Ocean', 'Mountain', 'Meadow']
        };

        // Get random name components
        const moodName = moodNames[mood][Math.floor(Math.random() * moodNames[mood].length)];
        const styleName = styleNames[style][Math.floor(Math.random() * styleNames[style].length)];

        return `${moodName} ${styleName}`;
    }

    // Generate a track description based on preferences
    function generateTrackDescription(mood, style, duration) {
        const moodDesc = {
            calm: 'calming and relaxing',
            focus: 'enhancing concentration and productivity',
            uplifting: 'elevating mood and energy',
            sleep: 'promoting deep relaxation and sleep'
        };

        const styleDesc = {
            ambient: 'ambient soundscapes',
            lofi: 'lo-fi beats',
            classical: 'classical-inspired melodies',
            nature: 'nature-infused sounds'
        };

        return `${duration} minute composition of ${styleDesc[style]} designed for ${moodDesc[mood]}.`;
    }

    // Breathing exercise functionality
    let breathingInterval;
    let isBreathing = false;

    startBreathingBtn.addEventListener('click', () => {
        if (isBreathing) {
            clearInterval(breathingInterval);
            breathingCircle.classList.remove('inhale', 'exhale');
            breathingInstruction.textContent = 'Click to begin';
            startBreathingBtn.textContent = 'Start Exercise';
            isBreathing = false;
        } else {
            let phase = 'inhale';
            breathingCircle.classList.add('inhale');
            breathingInstruction.textContent = 'Breathe in...';
            startBreathingBtn.textContent = 'Stop Exercise';
            isBreathing = true;

            breathingInterval = setInterval(() => {
                if (phase === 'inhale') {
                    phase = 'hold';
                    breathingInstruction.textContent = 'Hold...';
                    setTimeout(() => {
                        if (isBreathing) {
                            phase = 'exhale';
                            breathingCircle.classList.remove('inhale');
                            breathingCircle.classList.add('exhale');
                            breathingInstruction.textContent = 'Breathe out...';
                        }
                    }, 2000);
                } else if (phase === 'exhale') {
                    phase = 'rest';
                    breathingInstruction.textContent = 'Rest...';
                    setTimeout(() => {
                        if (isBreathing) {
                            phase = 'inhale';
                            breathingCircle.classList.remove('exhale');
                            breathingCircle.classList.add('inhale');
                            breathingInstruction.textContent = 'Breathe in...';
                        }
                    }, 2000);
                }
            }, 4000);
        }
    });

    // Stretching exercises
    const stretches = [
        { name: 'Neck Rolls', instruction: 'Gently roll your head in a circular motion', duration: 30 },
        { name: 'Shoulder Rolls', instruction: 'Roll your shoulders forward, then backward', duration: 30 },
        { name: 'Wrist Stretches', instruction: 'Extend your arm and gently pull your fingers back', duration: 20 },
        { name: 'Side Stretch', instruction: 'Raise your arm over your head and lean to the opposite side', duration: 20 },
        { name: 'Ankle Circles', instruction: 'Lift one foot and rotate your ankle in both directions', duration: 20 }
    ];

    let currentStretchIndex = 0;
    let stretchTimer;
    let stretchCountdown;

    function updateStretchDisplay() {
        const stretch = stretches[currentStretchIndex];
        stretchInstruction.textContent = `${stretch.name}: ${stretch.instruction}`;
        stretchTime.textContent = formatTime(stretch.duration);
    }

    function formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    updateStretchDisplay();

    prevStretchBtn.addEventListener('click', () => {
        if (stretchTimer) return; // Don't change during active stretch
        currentStretchIndex = (currentStretchIndex - 1 + stretches.length) % stretches.length;
        updateStretchDisplay();
    });

    nextStretchBtn.addEventListener('click', () => {
        if (stretchTimer) return; // Don't change during active stretch
        currentStretchIndex = (currentStretchIndex + 1) % stretches.length;
        updateStretchDisplay();
    });

    startStretchBtn.addEventListener('click', () => {
        if (stretchTimer) {
            // Stop the stretch
            clearInterval(stretchTimer);
            stretchTimer = null;
            stretchCountdown = null;
            updateStretchDisplay();
            startStretchBtn.textContent = 'Start';
            prevStretchBtn.disabled = false;
            nextStretchBtn.disabled = false;
        } else {
            // Start the stretch
            const stretch = stretches[currentStretchIndex];
            stretchCountdown = stretch.duration;
            stretchTime.textContent = formatTime(stretchCountdown);
            startStretchBtn.textContent = 'Stop';
            prevStretchBtn.disabled = true;
            nextStretchBtn.disabled = true;

            stretchTimer = setInterval(() => {
                stretchCountdown--;
                stretchTime.textContent = formatTime(stretchCountdown);

                if (stretchCountdown <= 0) {
                    clearInterval(stretchTimer);
                    stretchTimer = null;
                    startStretchBtn.textContent = 'Start';
                    prevStretchBtn.disabled = false;
                    nextStretchBtn.disabled = false;

                    // Auto-advance to next stretch
                    currentStretchIndex = (currentStretchIndex + 1) % stretches.length;
                    updateStretchDisplay();
                }
            }, 1000);
        }
    });

    // Mindfulness timer functionality
    let mindfulnessMinutes = 5;
    let mindfulnessSeconds = mindfulnessMinutes * 60;
    let mindfulnessInterval;

    timeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            timeButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            mindfulnessMinutes = parseInt(btn.getAttribute('data-time'));
            mindfulnessSeconds = mindfulnessMinutes * 60;
            mindfulnessTimer.textContent = `${mindfulnessMinutes}:00`;
        });
    });

    startMindfulnessBtn.addEventListener('click', () => {
        if (mindfulnessInterval) {
            // Stop the timer
            clearInterval(mindfulnessInterval);
            mindfulnessInterval = null;
            mindfulnessSeconds = mindfulnessMinutes * 60;
            mindfulnessTimer.textContent = `${mindfulnessMinutes}:00`;
            startMindfulnessBtn.textContent = 'Start Mindfulness Session';
            timeButtons.forEach(btn => btn.disabled = false);
        } else {
            // Start the timer
            startMindfulnessBtn.textContent = 'Stop Session';
            timeButtons.forEach(btn => btn.disabled = true);

            mindfulnessInterval = setInterval(() => {
                mindfulnessSeconds--;
                const mins = Math.floor(mindfulnessSeconds / 60);
                const secs = mindfulnessSeconds % 60;
                mindfulnessTimer.textContent = `${mins}:${secs.toString().padStart(2, '0')}`;

                if (mindfulnessSeconds <= 0) {
                    clearInterval(mindfulnessInterval);
                    mindfulnessInterval = null;
                    startMindfulnessBtn.textContent = 'Start Mindfulness Session';
                    timeButtons.forEach(btn => btn.disabled = false);

                    // Play sound when timer ends
                    const audio = new Audio('https://soundbible.com/mp3/Zen_Temple_Bell-SoundBible.com-331362457.mp3');
                    audio.play();
                }
            }, 1000);
        }
    });

    // Profile modal functionality
    profileButton.addEventListener('click', () => {
        updateProfileUI();
        profileModal.classList.add('show');
    });

    // Close profile modal when clicking the X button
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', () => {
            profileModal.classList.remove('show');
        });
    } else {
        console.error('Close modal button not found for profile modal');
    }

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === profileModal) {
            profileModal.classList.remove('show');
        }
    });

    saveProfileBtn.addEventListener('click', () => {
        // Save profile data
        userProfile.name = document.getElementById('profileName').value;
        userProfile.age = document.getElementById('profileAge').value;
        userProfile.grade = document.getElementById('profileGrade').value;
        userProfile.major = document.getElementById('profileMajor').value;

        // Get selected stressors
        userProfile.stressors = [];
        document.querySelectorAll('input[name="stressor"]:checked').forEach(checkbox => {
            userProfile.stressors.push(checkbox.value);
        });

        // Save to localStorage
        localStorage.setItem('userProfile', JSON.stringify(userProfile));

        // Update UI elements
        updateProfileUI();
        document.getElementById('profileAvatar').src = userProfile.avatarUrl;

        // Close modal
        profileModal.classList.remove('show');

        // Show feedback
        alert('Profile saved successfully!');
    });

    document.getElementById('uploadAvatarBtn').addEventListener('click', () => {
        document.getElementById('avatarFileInput').click();
    });

    document.getElementById('avatarFileInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;

        if (!file.type.match('image.*')) {
            alert('Please select an image file.');
            return;
        }

        const reader = new FileReader();

        reader.onload = function(event) {
            // Update profile preview and user's avatar
            userProfile.avatarUrl = event.target.result;
            document.getElementById('profileAvatarPreview').src = event.target.result;
            document.getElementById('profileAvatar').src = event.target.result;
        }

        reader.readAsDataURL(file);
    });

    // Update slider display
    sleepInput.addEventListener('input', () => updateSliderDisplay(sleepInput, sleepValueDisplay, 'hours'));
    homeworkInput.addEventListener('input', () => updateSliderDisplay(homeworkInput, homeworkValueDisplay));
    socialInput.addEventListener('input', () => updateSliderDisplay(socialInput, socialValueDisplay));
    extracurricularsInput.addEventListener('input', () => updateSliderDisplay(extracurricularsInput, extracurricularsValueDisplay));
    nutritionInput.addEventListener('input', () => updateSliderDisplay(nutritionInput, nutritionValueDisplay));
    screenInput.addEventListener('input', () => updateSliderDisplay(screenInput, screenValueDisplay, 'hours'));

    function updateSliderDisplay(input, display, unit = '') {
        display.textContent = input.value + (unit ? ` ${unit}` : '');
    }

    // Initialize stress history data
    if (stressHistory.length > 0) {
        updateStressHistory();
    }

    // Save the current stress result
    saveResultButton.addEventListener('click', () => {
        const stressLevel = stressLevelText.textContent;
        const emoji = stressEmoji.textContent;
        const advice = stressAdviceText.textContent;
        const score = parseInt(stressMeterFill.style.width) || 50;

        // Create a new record
        const newRecord = {
            date: new Date().toISOString(),
            level: stressLevel,
            score: score,
            emoji: emoji,
            advice: advice
        };

        // Add to history
        stressHistory.push(newRecord);

        // Keep only the last 30 days
        if (stressHistory.length > 30) {
            stressHistory = stressHistory.slice(-30);
        }

        // Save to localStorage
        localStorage.setItem('stressHistory', JSON.stringify(stressHistory));

        // Update display
        updateStressHistory();

        // Feedback to user
        saveResultButton.textContent = 'Saved!';
        setTimeout(() => {
            saveResultButton.textContent = 'Save Today\'s Result';
        }, 2000);
    });

    // Clear history
    clearHistoryButton.addEventListener('click', () => {
        if (confirm('Are you sure you want to clear all your stress history?')) {
            stressHistory = [];
            localStorage.removeItem('stressHistory');
            historySection.style.display = 'none';
        }
    });

    // Update stress history
    function updateStressHistory() {
        // Update history list
        historyList.innerHTML = '';

        if (stressHistory.length === 0) {
            historyList.innerHTML = '<p>No stress records yet. Save your first result!</p>';
            return;
        }

        stressHistory.forEach((record, index) => {
            const recordDate = new Date(record.date);
            const formattedDate = recordDate.toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric'
            });

            const levelClass = `level-${record.level.toLowerCase().replace(' ', '-')}`;

            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <span class="history-date">${formattedDate}</span>
                <span class="history-level ${levelClass}">${record.level} ${record.emoji}</span>
            `;
            historyList.appendChild(historyItem);
        });

        // Update chart
        const chartData = prepareChartData();
        updateStressChart(chartData);

        // Show history section
        historySection.style.display = 'block';
    }

    // Prepare chart data
    function prepareChartData() {
        // Sort records by date
        const sortedHistory = [...stressHistory].sort((a, b) => new Date(a.date) - new Date(b.date));

        // Prepare data for chart
        const labels = sortedHistory.map(record => {
            const date = new Date(record.date);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        const data = sortedHistory.map(record => record.score);

        return { labels, data };
    }

    // Update stress chart
    function updateStressChart(chartData) {
        // Destroy previous chart if it exists
        if (stressChartInstance) {
            stressChartInstance.destroy();
        }

        // Create new chart
        const ctx = stressChart.getContext('2d');
        stressChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: 'Stress Score',
                    data: chartData.data,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: '#3498db',
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Stress Score'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                const index = context.dataIndex;
                                const record = stressHistory.find(r => {
                                    const date = new Date(r.date);
                                    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) === context.label;
                                });
                                if (record) {
                                    return [
                                        `Level: ${record.level} ${record.emoji}`,
                                        `Advice: ${record.advice}`
                                    ];
                                }
                                return '';
                            }
                        }
                    }
                }
            }
        });
    }

    predictButton.addEventListener('click', async () => {
        resultArea.style.display = 'block';
        predictionOutput.style.display = 'none';
        loadingIndicator.style.display = 'block';
        document.body.className = ''; // Reset background color

        const studentData = {
            sleep: parseFloat(sleepInput.value),
            homeworkLoad: parseInt(homeworkInput.value),
            upcomingExams: parseInt(examsInput.value),
            socialInteraction: parseFloat(socialInput.value),
            extracurriculars: parseFloat(extracurricularsInput.value),
            nutrition: parseFloat(nutritionInput.value),
            screenTime: parseFloat(screenInput.value)
        };

        const systemPrompt = `You are a helpful AI assistant that predicts a student's daily stress level based on several factors. You also provide a brief, positive piece of advice and personalized tips. Be encouraging, concise, and provide actionable advice. Use a friendly, supportive tone.
Respond ONLY with a JSON object matching this schema:
{
  "stressLevel": "string (Low, Moderate, High, or Very High)",
  "stressScore": "number between 0 and 100, with 0 being no stress and 100 being maximum stress",
  "advice": "string (1-2 concise sentences of positive advice)",
  "emoji": "string (a single emoji representing the stress level or mood)",
  "tips": ["array of 3 brief, specific tips based on their inputs"]
}
Do not include any other text or explanations outside of the JSON object. Keep advice encouraging and actionable if possible.`;

        const userPrompt = `Predict the stress level for a student with the following daily factors:
- Sleep: ${studentData.sleep} hours
- Homework Load (0-10 scale): ${studentData.homeworkLoad}
- Number of Upcoming Exams: ${studentData.upcomingExams}
- Social Interaction (0-5 scale): ${studentData.socialInteraction}
- Extracurricular Activities (0-5 scale): ${studentData.extracurriculars}
- Nutrition Quality (0-5 scale): ${studentData.nutrition}
- Screen Time: ${studentData.screenTime} hours

Consider these factors together to determine stress level. For example, low sleep, high homework load, and many exams would indicate high stress. Good social interaction and nutrition might lower stress levels.`;

        try {
            // Create a system prompt that includes stress prediction if available
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: systemPrompt
                    },
                    {
                        role: "user",
                        content: userPrompt
                    }
                ],
                json: true,
            });

            // Parse the JSON response
            let result;
            try {
                // Handle potential HTML in the response by extracting just the JSON
                const jsonMatch = completion.content.match(/\{[\s\S]*\}/);
                const jsonString = jsonMatch ? jsonMatch[0] : completion.content;
                result = JSON.parse(jsonString);
            } catch (parseError) {
                console.error("Error parsing JSON response:", parseError);
                // Fallback to a simple object if parsing fails
                result = {
                    stressLevel: "Moderate",
                    stressScore: 50,
                    advice: "Take regular breaks and practice deep breathing.",
                    emoji: "😌",
                    tips: ["Take short breaks", "Stay hydrated", "Get enough sleep"]
                };
            }

            // Safely set text content to avoid HTML rendering issues
            stressLevelText.textContent = result.stressLevel || "N/A";
            stressEmoji.textContent = result.emoji || "🤔";
            stressAdviceText.textContent = result.advice || "No specific advice available.";

            // Update stress meter
            const stressScore = result.stressScore || 50;
            stressMeterFill.style.width = `${stressScore}%`;

            // Update personalized tips - using textContent to avoid HTML issues
            personalizedTips.innerHTML = '';
            if (result.tips && result.tips.length > 0) {
                const tipsHeading = document.createElement('h3');
                tipsHeading.textContent = 'Personalized Tips:';
                personalizedTips.appendChild(tipsHeading);

                const tipsList = document.createElement('ul');
                result.tips.forEach(tip => {
                    const tipItem = document.createElement('li');
                    tipItem.textContent = tip; // Use textContent to safely set content
                    tipsList.appendChild(tipItem);
                });
                personalizedTips.appendChild(tipsList);
            }

            // Update background based on stress level
            const stressClass = (result.stressLevel || "").toLowerCase().replace(' ', '-');
            if (['low', 'moderate', 'high', 'very-high'].includes(stressClass)) {
                 document.body.classList.add(`stress-${stressClass}`);
            }

            // Store the prediction for use in chat
            lastStressPrediction = {
                level: result.stressLevel || "Moderate",
                score: result.stressScore || 50,
                advice: result.advice || "",
                tips: result.tips || []
            };

            // Update websim with stress data if available
            if (typeof websim !== 'undefined' && typeof websim.updateStressData === 'function') {
                websim.updateStressData(lastStressPrediction, studentData);

                // Also dispatch an event for any listeners
                const event = new CustomEvent('stressDataUpdated', {
                    detail: {
                        prediction: lastStressPrediction,
                        studentData: studentData
                    }
                });
                document.dispatchEvent(event);
            }

        } catch (error) {
            console.error("Error fetching prediction:", error);
            stressLevelText.textContent = "Error";
            stressEmoji.textContent = "😟";
            stressAdviceText.textContent = "Could not retrieve prediction. Please try again.";
            document.body.className = ''; // Reset on error
        } finally {
            loadingIndicator.style.display = 'none';
            predictionOutput.style.display = 'block';
        }
    });

    // Call updateWelcomeMessage and updateGreetingText on page load
    updateWelcomeMessage();
    updateGreetingText();

    // Add direct event listener to profile modal close button
    const profileModalCloseBtn = document.querySelector('#profileModal .close-modal');
    if (profileModalCloseBtn) {
        profileModalCloseBtn.addEventListener('click', () => {
            profileModal.classList.remove('show');
        });
    }

    // AI Status Indicator functionality
    const aiStatusIndicator = document.getElementById('aiStatusIndicator');
    const aiStatusIcon = document.getElementById('aiStatusIcon');
    const aiStatusText = document.getElementById('aiStatusText');
    const onlineStatus = document.getElementById('onlineStatus');
    const offlineStatus = document.getElementById('offlineStatus');
    const currentModelName = document.getElementById('currentModelName');
    const aiPreferenceToggle = document.getElementById('aiPreferenceToggle');

    // Update AI status display
    function updateAIStatusDisplay(status, models) {
        if (!status || !models) return;

        // Update status indicator
        aiStatusIndicator.className = 'ai-status-indicator';

        if (status.currentModel === 'deepseek-online' || status.currentModel === 'deepseek-online-fallback') {
            aiStatusIndicator.classList.add('online');
            aiStatusText.textContent = 'Online';
            aiStatusIcon.textContent = '📡';
        } else if (status.currentModel === 'llama-offline' || status.currentModel === 'llama-offline-fallback') {
            aiStatusIndicator.classList.add('offline');
            aiStatusText.textContent = 'Offline';
            aiStatusIcon.textContent = '💻';
        } else {
            aiStatusIndicator.classList.add('error');
            aiStatusText.textContent = 'Error';
            aiStatusIcon.textContent = '⚠️';
        }

        // Update model status indicators
        onlineStatus.textContent = status.onlineAvailable ? '✅' : '❌';
        offlineStatus.textContent = status.offlineAvailable ? '✅' : '❌';

        // Update current model name
        const modelNames = {
            'deepseek-online': 'DeepSeek (Online)',
            'deepseek-online-fallback': 'DeepSeek (Fallback)',
            'llama-offline': 'Llama3.2 (Offline)',
            'llama-offline-fallback': 'Llama3.2 (Fallback)',
            'none': 'No AI Available'
        };
        currentModelName.textContent = modelNames[status.currentModel] || status.currentModel;

        // Update preference toggle
        aiPreferenceToggle.checked = status.preferOnline;
    }

    // Handle AI preference toggle
    if (aiPreferenceToggle) {
        aiPreferenceToggle.addEventListener('change', async () => {
            try {
                await websim.setAIPreference(aiPreferenceToggle.checked);
                console.log(`AI preference changed to: ${aiPreferenceToggle.checked ? 'Online' : 'Offline'}`);
            } catch (error) {
                console.error('Failed to change AI preference:', error);
                // Revert toggle on error
                aiPreferenceToggle.checked = !aiPreferenceToggle.checked;
            }
        });
    }

    // Listen for AI status updates
    document.addEventListener('aiStatusUpdated', (event) => {
        updateAIStatusDisplay(event.detail.status, event.detail.models);
    });

    // Listen for AI response events to show which model was used
    document.addEventListener('aiResponseReceived', (event) => {
        const { model, usage } = event.detail;
        console.log(`Response received from: ${model}`);

        // Briefly highlight the status indicator
        if (aiStatusIndicator) {
            aiStatusIndicator.style.transform = 'scale(1.05)';
            setTimeout(() => {
                aiStatusIndicator.style.transform = 'scale(1)';
            }, 200);
        }
    });

    // Initial AI status check
    if (typeof websim !== 'undefined' && websim.getAISystemStatus) {
        websim.getAISystemStatus();
    }
});