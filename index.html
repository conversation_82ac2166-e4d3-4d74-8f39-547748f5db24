<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Stress Predictor AI</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="nav-toggle-btn" id="navToggleBtn">
        <div class="toggle-icon"></div>
    </div>

    <nav class="main-nav" id="mainNav">
        <div class="logo">
            <img src="ai_stress_icon.png" alt="AI Stress Icon" class="logo-icon">
            <span>StressAI</span>
        </div>
        <ul class="nav-links">
            <li><a href="#" class="nav-item active" data-page="main">Predictor</a></li>
            <li><a href="#" class="nav-item" data-page="activities">Activities</a></li>
            <li><a href="#" class="nav-item" data-page="ai-chat">AI Chat</a></li>
        </ul>
        <div class="ai-status-indicator" id="aiStatusIndicator">
            <div class="ai-status-icon" id="aiStatusIcon">🤖</div>
            <div class="ai-status-text" id="aiStatusText">Checking...</div>
            <div class="ai-status-dropdown" id="aiStatusDropdown">
                <div class="ai-status-header">AI System Status</div>
                <div class="ai-model-info">
                    <div class="ai-model-item" id="onlineModelStatus">
                        <span class="model-icon">📡</span>
                        <span class="model-name">DeepSeek (Online)</span>
                        <span class="model-status" id="onlineStatus">❌</span>
                    </div>
                    <div class="ai-model-item" id="offlineModelStatus">
                        <span class="model-icon">💻</span>
                        <span class="model-name">Llama3.2 (Offline)</span>
                        <span class="model-status" id="offlineStatus">❌</span>
                    </div>
                </div>
                <div class="ai-preference-toggle">
                    <label for="aiPreferenceToggle">Prefer Online:</label>
                    <input type="checkbox" id="aiPreferenceToggle" checked>
                </div>
                <div class="ai-current-model" id="aiCurrentModel">
                    Current: <span id="currentModelName">Unknown</span>
                </div>
            </div>
        </div>
        <div class="nav-position-toggle">
            <button id="navPositionBtn">Move Nav</button>
        </div>
        <div class="profile-btn" id="profileButton">
            <img src="https://via.placeholder.com/40" alt="Profile" id="profileAvatar">
        </div>
    </nav>

    <div class="page-container">
        <!-- Main Page (Predictor) -->
        <div class="page" id="mainPage">
            <div class="container">
                <header>
                    <h1>Student Stress Predictor <span class="ai-badge">AI</span></h1>
                    <p id="greetingText">How are you feeling today? Let's find out!</p>
                </header>

                <div class="input-form">
                    <div class="input-group">
                        <label for="sleep">Hours of Sleep:</label>
                        <input type="range" id="sleep" name="sleep" min="0" max="12" value="8" step="0.5">
                        <span id="sleepValue">8 hours</span>
                    </div>

                    <div class="input-group">
                        <label for="homework">Homework Load (0-10 scale):</label>
                        <input type="range" id="homework" name="homework" min="0" max="10" value="5" step="1">
                        <span id="homeworkValue">5</span>
                    </div>

                    <div class="input-group">
                        <label for="exams">Number of Upcoming Exams:</label>
                        <input type="number" id="exams" name="exams" min="0" max="10" value="0">
                    </div>

                    <div class="input-group">
                        <label for="social">Social Interaction (0-5 scale, quality/hours):</label>
                        <input type="range" id="social" name="social" min="0" max="5" value="2" step="0.5">
                        <span id="socialValue">2</span>
                    </div>

                    <div class="input-group">
                        <label for="extracurriculars">Extracurriculars (0-5 scale, hours/commitment):</label>
                        <input type="range" id="extracurriculars" name="extracurriculars" min="0" max="5" value="1" step="0.5">
                        <span id="extracurricularsValue">1</span>
                    </div>

                    <div class="input-group">
                        <label for="nutrition">Nutrition Quality (0-5 scale):</label>
                        <input type="range" id="nutrition" name="nutrition" min="0" max="5" value="3" step="0.5">
                        <span id="nutritionValue">3</span>
                    </div>

                    <div class="input-group">
                        <label for="screen">Screen Time (hours):</label>
                        <input type="range" id="screen" name="screen" min="0" max="12" value="3" step="0.5">
                        <span id="screenValue">3 hours</span>
                    </div>

                    <button id="predictButton">Predict Stress Level</button>
                </div>

                <div id="resultArea" class="result-area" style="display: none;">
                    <h2>Prediction:</h2>
                    <div id="loadingIndicator" style="display: none;">
                        <div class="spinner"></div>
                        <p>AI is thinking...</p>
                    </div>
                    <div id="predictionOutput">
                        <div class="stress-meter-container">
                            <div class="stress-meter">
                                <div id="stressMeterFill"></div>
                            </div>
                            <div class="stress-labels">
                                <span>Low</span>
                                <span>Moderate</span>
                                <span>High</span>
                                <span>Very High</span>
                            </div>
                        </div>
                        <p><strong>Stress Level:</strong> <span id="stressLevelText"></span> <span id="stressEmoji"></span></p>
                        <p><strong>AI Advice:</strong> <span id="stressAdviceText"></span></p>
                        <div id="personalizedTips" class="personalized-tips"></div>
                        <button id="saveResultButton" class="save-button">Save Today's Result</button>
                    </div>
                </div>

                <div id="historySection" class="history-section" style="display: none;">
                    <h2>Your Stress History</h2>
                    <div class="chart-container">
                        <canvas id="stressChart"></canvas>
                    </div>
                    <div id="historyList" class="history-list"></div>
                    <button id="clearHistoryButton" class="clear-button">Clear History</button>
                </div>
            </div>
        </div>

        <!-- Activities Page -->
        <div class="page hidden" id="activitiesPage">
            <div class="container">
                <header>
                    <h1>Stress Relief Activities</h1>
                    <p>Try these activities to help manage your stress levels</p>
                </header>

                <div class="activities-grid">
                    <div class="activity-card">
                        <h3>AI Relaxation Music</h3>
                        <div class="music-player">
                            <div class="ai-music-generator">
                                <div class="ai-music-options">
                                    <div class="ai-option-group">
                                        <label>Mood:</label>
                                        <select id="aiMusicMood" class="ai-select">
                                            <option value="calm">Calm & Relaxed</option>
                                            <option value="focus">Focused & Productive</option>
                                            <option value="uplifting">Uplifting & Positive</option>
                                            <option value="sleep">Sleep & Deep Relaxation</option>
                                        </select>
                                    </div>
                                    <div class="ai-option-group">
                                        <label>Style:</label>
                                        <select id="aiMusicStyle" class="ai-select">
                                            <option value="ambient">Ambient</option>
                                            <option value="lofi">Lo-Fi</option>
                                            <option value="classical">Classical</option>
                                            <option value="nature">Nature Fusion</option>
                                        </select>
                                    </div>
                                    <div class="ai-option-group">
                                        <label>Duration:</label>
                                        <select id="aiMusicDuration" class="ai-select">
                                            <option value="short">2-3 minutes</option>
                                            <option value="medium" selected>5-7 minutes</option>
                                            <option value="long">10-15 minutes</option>
                                        </select>
                                    </div>
                                </div>
                                <button id="generateAiMusicBtn" class="activity-btn">Generate AI Music</button>

                                <div id="aiMusicResult" class="ai-music-result">
                                    <div class="ai-generating hidden">
                                        <div class="ai-generating-animation">
                                            <span></span><span></span><span></span>
                                        </div>
                                        <p>AI is composing your music...</p>
                                    </div>

                                    <div class="ai-track-player hidden">
                                        <div class="ai-track-info">
                                            <h4 id="aiTrackName">Your Custom Track</h4>
                                            <p id="aiTrackDescription">Personalized music based on your preferences</p>
                                        </div>
                                        <div class="ai-player-controls">
                                            <button id="playAiMusicBtn" class="play-btn">Play</button>
                                            <div class="ai-player-progress">
                                                <div id="aiMusicProgress" class="progress-bar">
                                                    <div id="aiMusicProgressFill" class="progress-fill"></div>
                                                </div>
                                                <span id="aiMusicTime">00:00</span>
                                            </div>
                                            <div class="ai-volume-control">
                                                <label for="aiVolume">Volume:</label>
                                                <input type="range" id="aiVolume" min="0" max="100" value="70">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="activity-card">
                        <h3>Breathing Exercises</h3>
                        <div class="breathing-exercise">
                            <div class="breathing-animation" id="breathingCircle"></div>
                            <p id="breathingInstruction">Click to begin</p>
                            <button id="startBreathingBtn" class="activity-btn">Start Exercise</button>
                        </div>
                    </div>

                    <div class="activity-card">
                        <h3>Quick Stretches</h3>
                        <div class="stretches">
                            <div class="stretch-timer">
                                <span id="stretchTime">00:30</span>
                            </div>
                            <p id="stretchInstruction">Neck Rolls: Gently roll your head in a circular motion</p>
                            <div class="stretch-controls">
                                <button id="prevStretchBtn" class="stretch-btn">◀</button>
                                <button id="startStretchBtn" class="activity-btn">Start</button>
                                <button id="nextStretchBtn" class="stretch-btn">▶</button>
                            </div>
                        </div>
                    </div>

                    <div class="activity-card">
                        <h3>Mindfulness Timer</h3>
                        <div class="mindfulness-timer">
                            <div class="timer-display" id="mindfulnessTimer">5:00</div>
                            <div class="timer-controls">
                                <button class="time-btn" data-time="3">3 min</button>
                                <button class="time-btn active" data-time="5">5 min</button>
                                <button class="time-btn" data-time="10">10 min</button>
                            </div>
                            <button id="startMindfulnessBtn" class="activity-btn">Start Mindfulness Session</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Chat Page -->
        <div class="page hidden" id="aiChatPage">
            <div class="container">
                <header>
                    <h1>Advanced AI Assistant <span class="ai-badge">GPT-4</span></h1>
                    <p>Intelligent conversational AI with advanced capabilities</p>
                </header>

                <div class="chat-container">
                    <div class="welcome-message" id="welcomeMessage">
                        <p>Hello! I'm your personal AI assistant. How can I help you today? I can answer questions, provide advice, and even recommend activities to help you manage your stress.</p>
                    </div>
                    <div class="chat-messages" id="chatMessages">
                        <!-- Messages will be added here dynamically -->
                    </div>

                    <div class="chat-controls">
                        <div class="chat-actions">
                            <button id="saveChatBtn" class="chat-action-btn" title="Save current chat">
                                <span class="action-icon">💾</span>
                            </button>
                            <button id="historyBtn" class="chat-action-btn" title="View chat history">
                                <span class="action-icon">📚</span>
                            </button>
                            <button id="exportChatBtn" class="chat-action-btn" title="Export current chat">
                                <span class="action-icon">📤</span>
                            </button>
                            <button id="clearChatBtn" class="chat-action-btn" title="Clear current chat">
                                <span class="action-icon">🗑️</span>
                            </button>
                        </div>
                        <div class="chat-status">
                            <span id="chatSaveStatus" class="save-status"></span>
                        </div>
                    </div>

                    <div class="chat-input">
                        <textarea id="userMessageInput" placeholder="Ask me anything..."></textarea>
                        <button id="sendMessageBtn" title="Send message"></button>
                    </div>

                    <div class="chat-suggestions">
                        <p>Advanced Capabilities</p>
                        <div class="suggestion-chips">
                            <button class="suggestion-chip" data-message="Explain quantum computing in simple terms">Explain concepts</button>
                            <button class="suggestion-chip" data-message="Write a Python function to analyze sentiment in text">Generate code</button>
                            <button class="suggestion-chip" data-message="What are the latest advancements in AI?">Research topics</button>
                            <button class="suggestion-chip" data-message="Help me debug this error: TypeError: Cannot read property 'map' of undefined">Debug code</button>
                            <button class="suggestion-chip" data-message="Create a study plan for finals week">Create plans</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat History Modal -->
        <div id="chatHistoryModal" class="modal">
            <div class="modal-content chat-history-modal">
                <span class="close-modal" id="closeChatHistoryBtn">&times;</span>
                <h2>Chat History</h2>
                <div class="chat-history-search">
                    <input type="text" id="chatHistorySearch" placeholder="Search your chat history...">
                    <button id="searchChatHistoryBtn">Search</button>
                </div>
                <div class="chat-history-container">
                    <div class="chat-history-list" id="chatHistoryList">
                        <!-- Chat history items will be added here dynamically -->
                        <div class="empty-history-message">No saved chats yet</div>
                    </div>
                    <div class="chat-history-preview" id="chatHistoryPreview">
                        <div class="preview-placeholder">
                            <p>Select a chat to preview</p>
                        </div>
                    </div>
                </div>
                <div class="chat-history-actions">
                    <button id="loadSelectedChatBtn" disabled>Load Selected Chat</button>
                    <button id="deleteSelectedChatBtn" disabled>Delete Selected Chat</button>
                    <button id="exportSelectedChatBtn" disabled>Export Selected Chat</button>
                    <button id="deleteAllChatsBtn">Delete All Chats</button>
                </div>
            </div>
        </div>

        <!-- Profile Modal -->
        <div id="profileModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h2>Your Profile</h2>
                <div class="profile-form">
                    <div class="profile-avatar-section">
                        <img id="profileAvatarPreview" src="https://via.placeholder.com/150" alt="Profile Avatar">
                        <div class="avatar-options">
                            <button id="changeAvatarBtn">Choose Avatar</button>
                            <button id="uploadAvatarBtn">Upload Image</button>
                            <input type="file" id="avatarFileInput" accept="image/*" style="display: none;">
                        </div>
                    </div>

                    <div class="profile-details">
                        <div class="input-group">
                            <label for="profileName">Name:</label>
                            <input type="text" id="profileName" placeholder="Your Name">
                        </div>

                        <div class="input-group">
                            <label for="profileAge">Age:</label>
                            <input type="number" id="profileAge" min="13" max="99">
                        </div>

                        <div class="input-group">
                            <label for="profileGrade">Grade/Year:</label>
                            <select id="profileGrade">
                                <option value="">Select Grade/Year</option>
                                <option value="high_school">High School</option>
                                <option value="freshman">College Freshman</option>
                                <option value="sophomore">College Sophomore</option>
                                <option value="junior">College Junior</option>
                                <option value="senior">College Senior</option>
                                <option value="graduate">Graduate Student</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="profileMajor">Major/Subject Focus:</label>
                            <input type="text" id="profileMajor" placeholder="Your Major or Subjects">
                        </div>

                        <div class="input-group">
                            <label>Common Stressors:</label>
                            <div class="checkbox-group">
                                <label><input type="checkbox" name="stressor" value="exams"> Exams</label>
                                <label><input type="checkbox" name="stressor" value="deadlines"> Deadlines</label>
                                <label><input type="checkbox" name="stressor" value="finances"> Finances</label>
                                <label><input type="checkbox" name="stressor" value="social"> Social Pressure</label>
                                <label><input type="checkbox" name="stressor" value="future"> Future Planning</label>
                            </div>
                        </div>
                    </div>

                    <button id="saveProfileBtn" class="save-button">Save Profile</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="websim.js"></script>
    <script src="script.js"></script>
</body>
</html>