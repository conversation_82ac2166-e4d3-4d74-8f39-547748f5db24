const axios = require('axios');

// OpenRouter Configuration
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const DEEPSEEK_MODEL_NAME = 'deepseek/deepseek-chat';

// Check OpenRouter API availability
async function checkOnlineAI() {
    try {
        const response = await axios.get(`${OPENROUTER_BASE_URL}/models`, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });
        
        console.log('✅ Online AI (DeepSeek via OpenRouter) is available');
        return true;
    } catch (error) {
        console.log('❌ Online AI (DeepSeek via OpenRouter) is unavailable:', error.message);
        return false;
    }
}

exports.handler = async (event, context) => {
    // Handle CORS
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        console.log('🔍 Checking AI system availability...');
        
        const onlineAvailable = await checkOnlineAI();
        
        const aiSystemStatus = {
            preferOnline: true,
            onlineAvailable: onlineAvailable,
            offlineAvailable: false, // Not available on Netlify
            currentModel: onlineAvailable ? 'deepseek-online' : 'none',
            lastCheck: Date.now()
        };

        console.log(`🤖 Current AI Model: ${aiSystemStatus.currentModel}`);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                status: aiSystemStatus,
                models: {
                    online: {
                        name: DEEPSEEK_MODEL_NAME,
                        provider: 'OpenRouter',
                        available: aiSystemStatus.onlineAvailable
                    },
                    offline: {
                        name: 'Not Available',
                        provider: 'Netlify Static Hosting',
                        available: false
                    }
                }
            })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Failed to get AI system status',
                details: error.message
            })
        };
    }
};
