const express = require('express');
const cors = require('cors');
const axios = require('axios');
const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('.'));

// Configuration for Ollama
const OLLAMA_BASE_URL = 'http://localhost:11434';
const MODEL_NAME = 'llama3.2:1b'; // Using the smaller 1B parameter version of Llama 3.2

// Helper function to enhance system prompt with stress data
function enhanceSystemPrompt(messages, stressData) {
    if (!stressData || !stressData.lastPrediction) return messages;

    // Find the system message
    const systemMessageIndex = messages.findIndex(msg => msg.role === 'system');
    if (systemMessageIndex === -1) {
        // If no system message exists, create one with stress data
        const stressInfo = createStressInfoPrompt(stressData);
        messages.unshift({
            role: 'system',
            content: stressInfo
        });
    } else {
        // Enhance existing system message with stress data
        const stressInfo = createStressInfoPrompt(stressData);
        messages[systemMessageIndex].content = `${stressInfo}\n\n${messages[systemMessageIndex].content}`;
    }

    return messages;
}

// Create a detailed prompt about the student's stress data
function createStressInfoPrompt(stressData) {
    const { lastPrediction, studentData } = stressData;

    let prompt = `You are a helpful AI assistant for students. The student has recently taken a stress assessment with the following results:`;

    if (lastPrediction) {
        prompt += `\n- Stress Level: ${lastPrediction.level}`;
        prompt += `\n- Stress Score: ${lastPrediction.score}/100`;

        if (lastPrediction.advice) {
            prompt += `\n- Advice Given: "${lastPrediction.advice}"`;
        }

        if (lastPrediction.tips && lastPrediction.tips.length > 0) {
            prompt += `\n- Tips Provided:`;
            lastPrediction.tips.forEach(tip => {
                prompt += `\n  * ${tip}`;
            });
        }
    }

    if (studentData) {
        prompt += `\n\nThe student's current situation:`;
        if (studentData.sleep !== undefined) prompt += `\n- Sleep: ${studentData.sleep} hours`;
        if (studentData.homeworkLoad !== undefined) prompt += `\n- Homework Load (0-10): ${studentData.homeworkLoad}`;
        if (studentData.upcomingExams !== undefined) prompt += `\n- Upcoming Exams: ${studentData.upcomingExams}`;
        if (studentData.socialInteraction !== undefined) prompt += `\n- Social Interaction (0-5): ${studentData.socialInteraction}`;
        if (studentData.extracurriculars !== undefined) prompt += `\n- Extracurricular Activities (0-5): ${studentData.extracurriculars}`;
        if (studentData.nutrition !== undefined) prompt += `\n- Nutrition Quality (0-5): ${studentData.nutrition}`;
        if (studentData.screenTime !== undefined) prompt += `\n- Screen Time: ${studentData.screenTime} hours`;
    }

    prompt += `\n\nKeep this information in mind when responding to the student. Be empathetic, supportive, and provide relevant advice based on their stress level and situation. Your responses should be concise, positive, and actionable.`;

    return prompt;
}

// API endpoint for chat completions
app.post('/api/chat/completions', async (req, res) => {
    try {
        const { messages, json, stressData } = req.body;

        // Enhanced messages with stress data if available
        const enhancedMessages = stressData ? enhanceSystemPrompt([...messages], stressData) : messages;

        // Log the enhanced messages for debugging
        console.log('Enhanced messages with stress data:', JSON.stringify(enhancedMessages, null, 2));

        // Format messages for Ollama API
        const ollamaRequest = {
            model: MODEL_NAME,
            messages: enhancedMessages,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9
            }
        };

        // Call Ollama API
        const response = await axios.post(`${OLLAMA_BASE_URL}/api/chat`, ollamaRequest);

        // Format response to match expected format
        const formattedResponse = {
            content: response.data.message.content,
            role: response.data.message.role
        };

        res.json(formattedResponse);
    } catch (error) {
        console.error('Error calling Ollama API:', error.message);
        res.status(500).json({
            error: 'Failed to get response from Ollama',
            details: error.message
        });
    }
});

// Start server
app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log(`Using Ollama model: ${MODEL_NAME}`);
});
