const express = require('express');
const cors = require('cors');
const axios = require('axios');
const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('.'));

// Configuration for Dual AI System
const OLLAMA_BASE_URL = 'http://localhost:11434';
const OLLAMA_MODEL_NAME = 'llama3.2:1b'; // Offline model

// OpenRouter Configuration for Online AI
const OPENROUTER_API_KEY = '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const DEEPSEEK_MODEL_NAME = 'deepseek/deepseek-chat'; // Online model

// AI System Status
let aiSystemStatus = {
    preferOnline: true,
    onlineAvailable: false,
    offlineAvailable: false,
    currentModel: 'unknown',
    lastCheck: null
};

// Check OpenRouter API availability
async function checkOnlineAI() {
    try {
        const response = await axios.get(`${OPENROUTER_BASE_URL}/models`, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });

        aiSystemStatus.onlineAvailable = response.status === 200;
        console.log('✅ Online AI (DeepSeek via OpenRouter) is available');
        return true;
    } catch (error) {
        aiSystemStatus.onlineAvailable = false;
        console.log('❌ Online AI (DeepSeek via OpenRouter) is unavailable:', error.message);
        return false;
    }
}

// Check Ollama availability
async function checkOfflineAI() {
    try {
        const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`, {
            timeout: 3000
        });

        // Check if our specific model is available
        const models = response.data.models || [];
        const hasModel = models.some(model => model.name.includes(OLLAMA_MODEL_NAME));

        aiSystemStatus.offlineAvailable = hasModel;
        if (hasModel) {
            console.log('✅ Offline AI (Llama3.2:1b via Ollama) is available');
        } else {
            console.log('⚠️ Ollama is running but Llama3.2:1b model not found');
        }
        return hasModel;
    } catch (error) {
        aiSystemStatus.offlineAvailable = false;
        console.log('❌ Offline AI (Ollama) is unavailable:', error.message);
        return false;
    }
}

// Determine which AI system to use
async function determineAISystem() {
    const now = Date.now();

    // Check systems every 30 seconds
    if (!aiSystemStatus.lastCheck || (now - aiSystemStatus.lastCheck) > 30000) {
        console.log('🔍 Checking AI system availability...');

        const [onlineOk, offlineOk] = await Promise.all([
            checkOnlineAI(),
            checkOfflineAI()
        ]);

        aiSystemStatus.lastCheck = now;

        // Determine current model based on availability and preference
        if (aiSystemStatus.preferOnline && onlineOk) {
            aiSystemStatus.currentModel = 'deepseek-online';
        } else if (offlineOk) {
            aiSystemStatus.currentModel = 'llama-offline';
        } else if (onlineOk) {
            aiSystemStatus.currentModel = 'deepseek-online';
        } else {
            aiSystemStatus.currentModel = 'none';
        }

        console.log(`🤖 Current AI Model: ${aiSystemStatus.currentModel}`);
    }

    return aiSystemStatus.currentModel;
}

// Handle online AI request (DeepSeek via OpenRouter)
async function handleOnlineAI(messages) {
    try {
        console.log('🌐 Using DeepSeek model via OpenRouter...');

        const response = await axios.post(`${OPENROUTER_BASE_URL}/chat/completions`, {
            model: DEEPSEEK_MODEL_NAME,
            messages: messages,
            temperature: 0.7,
            max_tokens: 1000,
            top_p: 0.9
        }, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:3000',
                'X-Title': 'Student Stress AI Assistant'
            },
            timeout: 30000
        });

        if (response.data && response.data.choices && response.data.choices[0]) {
            return {
                content: response.data.choices[0].message.content,
                role: response.data.choices[0].message.role,
                model: 'deepseek-online',
                usage: response.data.usage
            };
        } else {
            throw new Error('Invalid response format from OpenRouter API');
        }
    } catch (error) {
        console.error('❌ OpenRouter API error:', error.message);
        throw error;
    }
}

// Handle offline AI request (Llama via Ollama)
async function handleOfflineAI(messages) {
    try {
        console.log('💻 Using Llama3.2:1b model via Ollama...');

        const ollamaRequest = {
            model: OLLAMA_MODEL_NAME,
            messages: messages,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9
            }
        };

        const response = await axios.post(`${OLLAMA_BASE_URL}/api/chat`, ollamaRequest, {
            timeout: 60000
        });

        if (response.data && response.data.message) {
            return {
                content: response.data.message.content,
                role: response.data.message.role,
                model: 'llama-offline'
            };
        } else {
            throw new Error('Invalid response format from Ollama API');
        }
    } catch (error) {
        console.error('❌ Ollama API error:', error.message);
        throw error;
    }
}

// Helper function to enhance system prompt with stress data
function enhanceSystemPrompt(messages, stressData) {
    if (!stressData || !stressData.lastPrediction) return messages;

    // Find the system message
    const systemMessageIndex = messages.findIndex(msg => msg.role === 'system');
    if (systemMessageIndex === -1) {
        // If no system message exists, create one with stress data
        const stressInfo = createStressInfoPrompt(stressData);
        messages.unshift({
            role: 'system',
            content: stressInfo
        });
    } else {
        // Enhance existing system message with stress data
        const stressInfo = createStressInfoPrompt(stressData);
        messages[systemMessageIndex].content = `${stressInfo}\n\n${messages[systemMessageIndex].content}`;
    }

    return messages;
}

// Create a detailed prompt about the student's stress data
function createStressInfoPrompt(stressData) {
    const { lastPrediction, studentData } = stressData;

    let prompt = `You are a helpful AI assistant for students. The student has recently taken a stress assessment with the following results:`;

    if (lastPrediction) {
        prompt += `\n- Stress Level: ${lastPrediction.level}`;
        prompt += `\n- Stress Score: ${lastPrediction.score}/100`;

        if (lastPrediction.advice) {
            prompt += `\n- Advice Given: "${lastPrediction.advice}"`;
        }

        if (lastPrediction.tips && lastPrediction.tips.length > 0) {
            prompt += `\n- Tips Provided:`;
            lastPrediction.tips.forEach(tip => {
                prompt += `\n  * ${tip}`;
            });
        }
    }

    if (studentData) {
        prompt += `\n\nThe student's current situation:`;
        if (studentData.sleep !== undefined) prompt += `\n- Sleep: ${studentData.sleep} hours`;
        if (studentData.homeworkLoad !== undefined) prompt += `\n- Homework Load (0-10): ${studentData.homeworkLoad}`;
        if (studentData.upcomingExams !== undefined) prompt += `\n- Upcoming Exams: ${studentData.upcomingExams}`;
        if (studentData.socialInteraction !== undefined) prompt += `\n- Social Interaction (0-5): ${studentData.socialInteraction}`;
        if (studentData.extracurriculars !== undefined) prompt += `\n- Extracurricular Activities (0-5): ${studentData.extracurriculars}`;
        if (studentData.nutrition !== undefined) prompt += `\n- Nutrition Quality (0-5): ${studentData.nutrition}`;
        if (studentData.screenTime !== undefined) prompt += `\n- Screen Time: ${studentData.screenTime} hours`;
    }

    prompt += `\n\nKeep this information in mind when responding to the student. Be empathetic, supportive, and provide relevant advice based on their stress level and situation. Your responses should be concise, positive, and actionable.`;

    return prompt;
}

// API endpoint for chat completions with dual AI support
app.post('/api/chat/completions', async (req, res) => {
    try {
        const { messages, json, stressData } = req.body;

        // Enhanced messages with stress data if available
        const enhancedMessages = stressData ? enhanceSystemPrompt([...messages], stressData) : messages;

        // Log the enhanced messages for debugging
        console.log('📝 Enhanced messages with stress data:', JSON.stringify(enhancedMessages, null, 2));

        // Determine which AI system to use
        const aiModel = await determineAISystem();

        let response;
        let modelUsed = aiModel;

        if (aiModel === 'deepseek-online') {
            try {
                response = await handleOnlineAI(enhancedMessages);
            } catch (error) {
                console.log('🔄 Online AI failed, falling back to offline...');
                if (aiSystemStatus.offlineAvailable) {
                    response = await handleOfflineAI(enhancedMessages);
                    modelUsed = 'llama-offline-fallback';
                } else {
                    throw error;
                }
            }
        } else if (aiModel === 'llama-offline') {
            try {
                response = await handleOfflineAI(enhancedMessages);
            } catch (error) {
                console.log('🔄 Offline AI failed, trying online...');
                if (aiSystemStatus.onlineAvailable) {
                    response = await handleOnlineAI(enhancedMessages);
                    modelUsed = 'deepseek-online-fallback';
                } else {
                    throw error;
                }
            }
        } else {
            throw new Error('No AI system available');
        }

        // Format response to match expected format
        const formattedResponse = {
            content: response.content,
            role: response.role,
            model: modelUsed,
            usage: response.usage || null
        };

        console.log(`✅ Response generated using: ${modelUsed}`);
        res.json(formattedResponse);

    } catch (error) {
        console.error('❌ Error in dual AI system:', error.message);
        res.status(500).json({
            error: 'Failed to get response from AI system',
            details: error.message,
            availableModels: {
                online: aiSystemStatus.onlineAvailable,
                offline: aiSystemStatus.offlineAvailable
            }
        });
    }
});

// API endpoint to get AI system status
app.get('/api/ai/status', async (req, res) => {
    try {
        await determineAISystem();
        res.json({
            status: aiSystemStatus,
            models: {
                online: {
                    name: DEEPSEEK_MODEL_NAME,
                    provider: 'OpenRouter',
                    available: aiSystemStatus.onlineAvailable
                },
                offline: {
                    name: OLLAMA_MODEL_NAME,
                    provider: 'Ollama',
                    available: aiSystemStatus.offlineAvailable
                }
            }
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to get AI system status',
            details: error.message
        });
    }
});

// API endpoint to switch AI preference
app.post('/api/ai/preference', (req, res) => {
    try {
        const { preferOnline } = req.body;
        if (typeof preferOnline === 'boolean') {
            aiSystemStatus.preferOnline = preferOnline;
            aiSystemStatus.lastCheck = null; // Force recheck
            console.log(`🔧 AI preference changed to: ${preferOnline ? 'Online (DeepSeek)' : 'Offline (Llama)'}`);
            res.json({ success: true, preferOnline: aiSystemStatus.preferOnline });
        } else {
            res.status(400).json({ error: 'Invalid preference value' });
        }
    } catch (error) {
        res.status(500).json({
            error: 'Failed to update AI preference',
            details: error.message
        });
    }
});

// Start server with AI system initialization
app.listen(port, () => {
    console.log(`🚀 Server running at http://localhost:${port}`);
    console.log('🤖 Dual AI System Ready');
    console.log(`   📡 Online Model: ${DEEPSEEK_MODEL_NAME} (via OpenRouter)`);
    console.log(`   💻 Offline Model: ${OLLAMA_MODEL_NAME} (via Ollama)`);
    console.log('✅ Server started successfully');
});
