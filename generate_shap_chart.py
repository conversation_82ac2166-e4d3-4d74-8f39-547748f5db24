import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import shap
import matplotlib.pyplot as plt

# Step 1: Generate synthetic dataset (mimicking 170+ survey responses)
np.random.seed(42)
n_samples = 170
data = {
    'sleep': np.random.uniform(4, 10, n_samples),  # 4-10 hours (from index.html slider)
    'homeworkLoad': np.random.randint(0, 11, n_samples),  # 0-10 scale
    'upcomingExams': np.random.randint(0, 5, n_samples),  # 0-4 exams
    'socialInteraction': np.random.uniform(0, 5, n_samples),  # 0-5 scale
    'extracurriculars': np.random.uniform(0, 5, n_samples),  # 0-5 scale
    'nutrition': np.random.uniform(0, 5, n_samples),  # 0-5 scale
    'screenTime': np.random.uniform(0, 10, n_samples),  # 0-10 hours
    'stress_level': np.random.choice(['Low', 'Moderate', 'High', 'Very High'], n_samples, p=[0.3, 0.4, 0.2, 0.1])
}
df = pd.DataFrame(data)

# Encode stress_level as categorical
stress_mapping = {'Low': 0, 'Moderate': 1, 'High': 2, 'Very High': 3}
df['stress_level'] = df['stress_level'].map(stress_mapping)

# Features and target
features = ['sleep', 'homeworkLoad', 'upcomingExams', 'socialInteraction', 'extracurriculars', 'nutrition', 'screenTime']
X = df[features]
y = df['stress_level']

# Step 2: Preprocess data
# Handle missing values (median imputation, as per Chapter Three)
X = X.fillna(X.median())
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
X_scaled = pd.DataFrame(X_scaled, columns=features)  # Retain column names

# Step 3: Split data and train Random Forest model
X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.15, random_state=42)
rf_model = RandomForestClassifier(
    n_estimators=100,
    max_depth=10,
    min_samples_split=2,
    random_state=42
)
rf_model.fit(X_train, y_train)

# Step 4: Compute SHAP values
explainer = shap.TreeExplainer(rf_model)
shap_values = explainer.shap_values(X_test)

# Step 5: Create SHAP summary plot for High stress class
plt.figure(figsize=(10, 6))
shap.summary_plot(shap_values[2], X_test, feature_names=features, show=False)  # Class 2 (High stress)
plt.title('SHAP Values for Stress Prediction (High Stress Class, Random Forest)')
plt.tight_layout()

# Step 6: Save the plot
plt.savefig('./media/shap_values.png', dpi=300, bbox_inches='tight')
plt.close()

print("SHAP value chart saved as './media/shap_values.png'")
```