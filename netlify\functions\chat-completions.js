const axios = require('axios');

// OpenRouter Configuration
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const DEEPSEEK_MODEL_NAME = 'deepseek/deepseek-chat';

// Helper function to enhance system prompt with stress data
function enhanceSystemPrompt(messages, stressData) {
    if (!stressData || !stressData.lastPrediction) return messages;

    // Find the system message
    const systemMessageIndex = messages.findIndex(msg => msg.role === 'system');
    if (systemMessageIndex === -1) {
        // If no system message exists, create one with stress data
        const stressInfo = createStressInfoPrompt(stressData);
        messages.unshift({
            role: 'system',
            content: stressInfo
        });
    } else {
        // Enhance existing system message with stress data
        const stressInfo = createStressInfoPrompt(stressData);
        messages[systemMessageIndex].content = `${stressInfo}\n\n${messages[systemMessageIndex].content}`;
    }

    return messages;
}

// Create a detailed prompt about the student's stress data
function createStressInfoPrompt(stressData) {
    const { lastPrediction, studentData } = stressData;

    let prompt = `You are a helpful AI assistant for students. The student has recently taken a stress assessment with the following results:`;

    if (lastPrediction) {
        prompt += `\n- Stress Level: ${lastPrediction.level}`;
        prompt += `\n- Stress Score: ${lastPrediction.score}/100`;

        if (lastPrediction.advice) {
            prompt += `\n- Advice Given: "${lastPrediction.advice}"`;
        }

        if (lastPrediction.tips && lastPrediction.tips.length > 0) {
            prompt += `\n- Tips Provided:`;
            lastPrediction.tips.forEach(tip => {
                prompt += `\n  * ${tip}`;
            });
        }
    }

    if (studentData) {
        prompt += `\n\nThe student's current situation:`;
        if (studentData.sleep !== undefined) prompt += `\n- Sleep: ${studentData.sleep} hours`;
        if (studentData.homeworkLoad !== undefined) prompt += `\n- Homework Load (0-10): ${studentData.homeworkLoad}`;
        if (studentData.upcomingExams !== undefined) prompt += `\n- Upcoming Exams: ${studentData.upcomingExams}`;
        if (studentData.socialInteraction !== undefined) prompt += `\n- Social Interaction (0-5): ${studentData.socialInteraction}`;
        if (studentData.extracurriculars !== undefined) prompt += `\n- Extracurricular Activities (0-5): ${studentData.extracurriculars}`;
        if (studentData.nutrition !== undefined) prompt += `\n- Nutrition Quality (0-5): ${studentData.nutrition}`;
        if (studentData.screenTime !== undefined) prompt += `\n- Screen Time: ${studentData.screenTime} hours`;
    }

    prompt += `\n\nKeep this information in mind when responding to the student. Be empathetic, supportive, and provide relevant advice based on their stress level and situation. Your responses should be concise, positive, and actionable.`;

    return prompt;
}

// Handle online AI request (DeepSeek via OpenRouter)
async function handleOnlineAI(messages) {
    try {
        console.log('🌐 Using DeepSeek model via OpenRouter...');
        
        const response = await axios.post(`${OPENROUTER_BASE_URL}/chat/completions`, {
            model: DEEPSEEK_MODEL_NAME,
            messages: messages,
            temperature: 0.7,
            max_tokens: 1000,
            top_p: 0.9
        }, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://your-netlify-site.netlify.app',
                'X-Title': 'Student Stress AI Assistant'
            },
            timeout: 30000
        });

        if (response.data && response.data.choices && response.data.choices[0]) {
            return {
                content: response.data.choices[0].message.content,
                role: response.data.choices[0].message.role,
                model: 'deepseek-online',
                usage: response.data.usage
            };
        } else {
            throw new Error('Invalid response format from OpenRouter API');
        }
    } catch (error) {
        console.error('❌ OpenRouter API error:', error.message);
        throw error;
    }
}

exports.handler = async (event, context) => {
    // Handle CORS
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const { messages, stressData } = JSON.parse(event.body);

        // Enhanced messages with stress data if available
        const enhancedMessages = stressData ? enhanceSystemPrompt([...messages], stressData) : messages;

        console.log('📝 Enhanced messages with stress data:', JSON.stringify(enhancedMessages, null, 2));

        // Use online AI (DeepSeek via OpenRouter)
        const response = await handleOnlineAI(enhancedMessages);

        // Format response
        const formattedResponse = {
            content: response.content,
            role: response.role,
            model: 'deepseek-online',
            usage: response.usage || null
        };

        console.log(`✅ Response generated using: deepseek-online`);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(formattedResponse)
        };
        
    } catch (error) {
        console.error('❌ Error in AI system:', error.message);
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Failed to get response from AI system',
                details: error.message,
                model: 'deepseek-online-only'
            })
        };
    }
};
