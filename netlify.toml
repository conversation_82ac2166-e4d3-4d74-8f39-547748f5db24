[build]
  publish = "."
  command = "echo 'Static build complete'"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://openrouter.ai https://api.openrouter.ai; media-src 'self' data: blob:;"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Environment variables for production
[context.production.environment]
  OPENROUTER_API_KEY = "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"
  NODE_ENV = "production"

# Environment variables for deploy previews
[context.deploy-preview.environment]
  OPENROUTER_API_KEY = "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"
  NODE_ENV = "development"
